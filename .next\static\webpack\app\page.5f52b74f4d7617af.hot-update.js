"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_InstallApp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/InstallApp */ \"(app-pages-browser)/./src/components/InstallApp.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isClearing, setIsClearing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Silent version check on homepage load (no user disruption)\n            const checkVersion = {\n                \"HomePage.useEffect.checkVersion\": async ()=>{\n                    try {\n                        const { checkVersionAndClearCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_versionService_ts-_d5ae0\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/versionService */ \"(app-pages-browser)/./src/lib/versionService.ts\"));\n                        await checkVersionAndClearCache();\n                    } catch (error) {\n                        console.error('Silent version check failed:', error);\n                    }\n                }\n            }[\"HomePage.useEffect.checkVersion\"];\n            checkVersion();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Version info for cache management\n    const appVersion = \"2.1.0\";\n    const lastUpdated = \"2025-01-19\";\n    const clearCacheAndData = async ()=>{\n        try {\n            setIsClearing(true);\n            // Show confirmation dialog\n            const result = await sweetalert2__WEBPACK_IMPORTED_MODULE_5___default().fire({\n                title: 'Clear Cache & Data?',\n                html: '\\n          <div class=\"text-left\">\\n            <p class=\"mb-3\"><strong>This will clear:</strong></p>\\n            <ul class=\"text-sm space-y-1 mb-4\">\\n              <li>• Browser cache & stored data</li>\\n              <li>• Local storage & session data</li>\\n              <li>• Cookies & preferences</li>\\n              <li>• Cached videos & images</li>\\n              <li>• All temporary files</li>\\n            </ul>\\n            <p class=\"text-sm text-gray-600\">\\n              <strong>Why clear cache?</strong> Get the latest version of MyTube with all new features and bug fixes.\\n            </p>\\n          </div>\\n        ',\n                icon: 'question',\n                showCancelButton: true,\n                confirmButtonColor: '#ef4444',\n                cancelButtonColor: '#6b7280',\n                confirmButtonText: 'Yes, Clear Everything!',\n                cancelButtonText: 'Cancel',\n                allowOutsideClick: false\n            });\n            if (!result.isConfirmed) {\n                setIsClearing(false);\n                return;\n            }\n            // Show progress\n            sweetalert2__WEBPACK_IMPORTED_MODULE_5___default().fire({\n                title: 'Clearing Cache & Data...',\n                html: '\\n          <div class=\"text-center\">\\n            <div class=\"spinner mx-auto mb-4\"></div>\\n            <p>Please wait while we clear all cached data...</p>\\n            <p class=\"text-sm text-gray-600 mt-2\">This may take a few seconds</p>\\n          </div>\\n        ',\n                allowOutsideClick: false,\n                allowEscapeKey: false,\n                showConfirmButton: false\n            });\n            // Clear localStorage\n            try {\n                localStorage.clear();\n                console.log('✅ localStorage cleared');\n            } catch (error) {\n                console.warn('⚠️ Could not clear localStorage:', error);\n            }\n            // Clear sessionStorage\n            try {\n                sessionStorage.clear();\n                console.log('✅ sessionStorage cleared');\n            } catch (error) {\n                console.warn('⚠️ Could not clear sessionStorage:', error);\n            }\n            // Clear IndexedDB (if available) with timeout protection\n            try {\n                if ('indexedDB' in window) {\n                    // Use known database names instead of indexedDB.databases() which might not be supported\n                    const knownDatabases = [\n                        'firebaseLocalStorageDb',\n                        'firebase-heartbeat-database',\n                        'firebase-installations-database'\n                    ];\n                    for (const dbName of knownDatabases){\n                        try {\n                            await Promise.race([\n                                new Promise((resolve, reject)=>{\n                                    const deleteReq = indexedDB.deleteDatabase(dbName);\n                                    deleteReq.onsuccess = ()=>resolve(true);\n                                    deleteReq.onerror = ()=>resolve(true) // Don't fail if DB doesn't exist\n                                    ;\n                                    deleteReq.onblocked = ()=>resolve(true) // Continue even if blocked\n                                    ;\n                                }),\n                                new Promise((_, reject)=>setTimeout(()=>reject(new Error('Timeout')), 3000))\n                            ]);\n                        } catch (error) {\n                            console.warn(\"⚠️ Could not clear IndexedDB \".concat(dbName, \":\"), error);\n                        }\n                    }\n                    console.log('✅ IndexedDB cleared');\n                }\n            } catch (error) {\n                console.warn('⚠️ Could not clear IndexedDB:', error);\n            }\n            // Clear Service Worker cache (if available) with timeout protection\n            try {\n                if ('serviceWorker' in navigator && 'caches' in window) {\n                    const cacheNames = await Promise.race([\n                        caches.keys(),\n                        new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache keys timeout')), 5000))\n                    ]);\n                    await Promise.race([\n                        Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName))),\n                        new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache deletion timeout')), 5000))\n                    ]);\n                    console.log('✅ Service Worker cache cleared');\n                }\n            } catch (error) {\n                console.warn('⚠️ Could not clear Service Worker cache:', error);\n            }\n            // Clear cookies (limited by same-origin policy)\n            try {\n                document.cookie.split(\";\").forEach((cookie)=>{\n                    const eqPos = cookie.indexOf(\"=\");\n                    const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;\n                    document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/\");\n                    document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=\").concat(window.location.hostname);\n                    document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.\").concat(window.location.hostname);\n                });\n                console.log('✅ Cookies cleared');\n            } catch (error) {\n                console.warn('⚠️ Could not clear cookies:', error);\n            }\n            // Wait a moment for operations to complete\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Show success and reload\n            sweetalert2__WEBPACK_IMPORTED_MODULE_5___default().fire({\n                icon: 'success',\n                title: 'Cache & Data Cleared!',\n                html: '\\n          <div class=\"text-center\">\\n            <p class=\"mb-3\">✅ All cached data has been cleared successfully!</p>\\n            <p class=\"text-sm text-gray-600 mb-3\">\\n              The page will now reload to load the latest version of MyTube.\\n            </p>\\n            <p class=\"text-sm text-green-600 font-semibold\">\\n              \\uD83C\\uDF89 You now have the freshest version with all updates!\\n            </p>\\n          </div>\\n        ',\n                timer: 3000,\n                showConfirmButton: true,\n                confirmButtonText: 'Reload Now',\n                allowOutsideClick: false\n            }).then(()=>{\n                // Force reload with cache bypass\n                window.location.reload();\n            });\n        } catch (error) {\n            console.error('Error clearing cache and data:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_5___default().fire({\n                icon: 'error',\n                title: 'Clear Failed',\n                html: '\\n          <div class=\"text-left\">\\n            <p class=\"mb-2\">Some data could not be cleared:</p>\\n            <p class=\"text-sm text-gray-600\">'.concat(error instanceof Error ? error.message : 'Unknown error', '</p>\\n            <p class=\"text-sm text-blue-600 mt-3\">\\n              Try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)\\n            </p>\\n          </div>\\n        '),\n                confirmButtonText: 'OK'\n            });\n        } finally{\n            setIsClearing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 32,\n                                    height: 32,\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xl font-bold\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearCacheAndData,\n                                    disabled: isClearing,\n                                    className: \"nav-link text-sm sm:text-base\",\n                                    title: \"Clear cache & data to get latest version\",\n                                    children: isClearing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"spinner w-3 h-3 mr-1 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Clearing...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-sync-alt mr-1 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Clear Cache\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"Cache\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"#pricing\",\n                                    className: \"nav-link text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-crown mr-1 sm:mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    className: \"nav-link text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sign-in-alt mr-1 sm:mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/img/mytube-logo.svg\",\n                                            alt: \"MyTube Logo\",\n                                            width: 60,\n                                            height: 60,\n                                            className: \"mr-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-4xl font-bold text-white\",\n                                            children: \"MyTube\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-7xl font-bold mb-6 gradient-text\",\n                                    children: \"Watch Videos & Earn Money\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto\",\n                                    children: \"Watch videos and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple video watching tasks!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-6 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle text-4xl text-youtube-red mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Trending Videos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80\",\n                                            children: \"Watch popular content daily\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-money-bill-wave text-4xl text-green-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Instant Earnings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80\",\n                                            children: \"Get paid for every video watched\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-bolt text-4xl text-yellow-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Fast & Simple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80\",\n                                            children: \"Easy video watching process\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    className: \"btn-primary inline-flex items-center text-lg px-8 py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-rocket mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Start Earning Now\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/how-it-works\",\n                                            className: \"btn-secondary inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-info-circle mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"How It Works\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/faq\",\n                                            className: \"btn-secondary inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-question-circle mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"FAQ\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                    children: \"Choose Your Earning Plan\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/80 max-w-3xl mx-auto\",\n                                    children: \"Start with our free trial or upgrade to premium plans for higher earnings. Watch videos and earn money with flexible pricing options.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white mb-2\",\n                                                    children: \"Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-white\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/60 ml-2\",\n                                                            children: \"/ 2 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-400 font-semibold\",\n                                                    children: \"Earn ₹10 per 50 videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"2 days access\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"₹10 per 50 videos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Basic support\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Video duration: 30 seconds\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"w-full btn-secondary block text-center\",\n                                            children: \"Start Free Trial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8 relative ring-2 ring-yellow-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold\",\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white mb-2\",\n                                                    children: \"Gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-white\",\n                                                            children: \"₹3,999\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/60 ml-2\",\n                                                            children: \"/ 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-400 font-semibold\",\n                                                    children: \"Earn ₹200 per 50 videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"30 days access\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"₹200 per 50 videos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Video duration: 3 minutes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Referral bonus: ₹400\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Priority support\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/plans\",\n                                            className: \"w-full bg-yellow-400 text-black py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center\",\n                                            children: \"Choose Gold\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white mb-2\",\n                                                    children: \"Diamond\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-white\",\n                                                            children: \"₹9,999\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/60 ml-2\",\n                                                            children: \"/ 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-400 font-semibold\",\n                                                    children: \"Earn ₹400 per 50 videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"30 days access\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"₹400 per 50 videos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Video duration: 1 minute\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Referral bonus: ₹1200\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-white/80\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-check text-green-400 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"VIP support\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/plans\",\n                                            className: \"w-full btn-primary block text-center\",\n                                            children: \"Choose Diamond\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/plans\",\n                                className: \"btn-secondary inline-flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-crown mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Plans\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                            children: \"Need Help?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/80 mb-12 max-w-2xl mx-auto\",\n                            children: \"Our support team is here to help you get started and answer any questions about earning with MyTube.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glass-card p-8 mb-12 max-w-2xl mx-auto clear-cache-section\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-sync-alt text-5xl text-blue-400 mb-4 clear-cache-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Get Latest Version\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 mb-6\",\n                                        children: \"Having issues or want the latest features? Clear your cache and data to get the freshest version of MyTube with all updates and bug fixes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearCacheAndData,\n                                        disabled: isClearing,\n                                        className: \"clear-cache-btn inline-flex items-center text-lg px-8 py-4 text-white font-semibold rounded-lg \".concat(isClearing ? 'btn-processing' : ''),\n                                        children: isClearing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-3 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Clearing Cache...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-sync-alt mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Clear Cache & Data\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm mt-4\",\n                                        children: \"✨ Recommended if you're experiencing issues or want the latest features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 inline-flex items-center bg-white/10 rounded-full px-4 py-2 text-xs text-white/80\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-code-branch mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Version \",\n                                            appVersion,\n                                            \" • Updated \",\n                                            lastUpdated\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 grid grid-cols-2 gap-4 text-xs text-white/60\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-database mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Clears Storage\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-cookie-bite mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Removes Cookies\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-memory mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Clears Cache\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-download mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Fresh Download\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://wa.me/917676636990\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"glass-card p-8 hover:scale-105 transition-transform\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fab fa-whatsapp text-5xl text-green-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-2\",\n                                            children: \"WhatsApp Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-4\",\n                                            children: \"Get instant help via WhatsApp\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-400 font-semibold\",\n                                            children: \"+91 7676636990\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm mt-2\",\n                                            children: \"9 AM - 6 PM (Working days)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"glass-card p-8 hover:scale-105 transition-transform\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-envelope text-5xl text-blue-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-2\",\n                                            children: \"Email Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-4\",\n                                            children: \"Send us detailed queries\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-400 font-semibold\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm mt-2\",\n                                            children: \"9 AM - 6 PM (Working days)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstallApp__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    variant: \"homepage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-12 px-4 border-t border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 md:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-white mb-2\",\n                                        children: \"MyTube\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60\",\n                                        children: \"Earn money by watching videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"\\xa9 2024 MyTube. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 flex items-center justify-center md:justify-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-code-branch mr-2 text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs\",\n                                                children: [\n                                                    \"v\",\n                                                    appVersion\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mx-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearCacheAndData,\n                                                disabled: isClearing,\n                                                className: \"text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 underline\",\n                                                children: isClearing ? 'Clearing...' : 'Clear Cache'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"ktW2F4L0BeigsT/kb4FIiHcj6qs=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});