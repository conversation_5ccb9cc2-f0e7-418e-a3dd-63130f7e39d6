"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_leaveService_ts";
exports.ids = ["_ssr_src_lib_leaveService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/leaveService.ts":
/*!*********************************!*\
  !*** ./src/lib/leaveService.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyUserLeave: () => (/* binding */ applyUserLeave),\n/* harmony export */   calculateActiveDays: () => (/* binding */ calculateActiveDays),\n/* harmony export */   cancelUserLeave: () => (/* binding */ cancelUserLeave),\n/* harmony export */   createAdminLeave: () => (/* binding */ createAdminLeave),\n/* harmony export */   debugAdminLeaveStatus: () => (/* binding */ debugAdminLeaveStatus),\n/* harmony export */   deleteAdminLeave: () => (/* binding */ deleteAdminLeave),\n/* harmony export */   getAdminLeaves: () => (/* binding */ getAdminLeaves),\n/* harmony export */   getAllUserLeaves: () => (/* binding */ getAllUserLeaves),\n/* harmony export */   getUserLeaves: () => (/* binding */ getUserLeaves),\n/* harmony export */   getUserMonthlyLeaveCount: () => (/* binding */ getUserMonthlyLeaveCount),\n/* harmony export */   isAdminLeaveDay: () => (/* binding */ isAdminLeaveDay),\n/* harmony export */   isUserOnLeave: () => (/* binding */ isUserOnLeave),\n/* harmony export */   isWorkBlocked: () => (/* binding */ isWorkBlocked),\n/* harmony export */   updateUserLeaveStatus: () => (/* binding */ updateUserLeaveStatus)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\nconst COLLECTIONS = {\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Admin Leave Functions\nasync function createAdminLeave(leaveData) {\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n        });\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating admin leave:', error);\n        throw error;\n    }\n}\nasync function getAdminLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'asc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                createdAt: doc.data().createdAt.toDate()\n            }));\n        console.log('📅 All admin leaves:', leaves);\n        return leaves;\n    } catch (error) {\n        console.error('Error getting admin leaves:', error);\n        throw error;\n    }\n}\n// Debug function to check current admin leave status\nasync function debugAdminLeaveStatus() {\n    try {\n        const today = new Date();\n        console.log('🔍 Debug: Checking admin leave status for today:', today.toDateString());\n        const isLeave = await isAdminLeaveDay(today);\n        console.log('📊 Debug: Admin leave result:', isLeave);\n        const allLeaves = await getAdminLeaves();\n        console.log('📅 Debug: All admin leaves in database:', allLeaves);\n        const todayLeaves = allLeaves.filter((leave)=>leave.date.toDateString() === today.toDateString());\n        console.log('📅 Debug: Today\\'s admin leaves:', todayLeaves);\n    } catch (error) {\n        console.error('❌ Debug: Error checking admin leave status:', error);\n    }\n}\nasync function deleteAdminLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves, leaveId));\n    } catch (error) {\n        console.error('Error deleting admin leave:', error);\n        throw error;\n    }\n}\nasync function isAdminLeaveDay(date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking admin leave for date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasAdminLeave = !querySnapshot.empty;\n        if (hasAdminLeave) {\n            console.log('📅 Found admin leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('📅 No admin leaves found for today');\n        }\n        return hasAdminLeave;\n    } catch (error) {\n        console.error('❌ Error checking admin leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// User Leave Functions\nasync function applyUserLeave(leaveData) {\n    try {\n        // Check if user has available leave quota for automatic approval\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const currentMonth = currentDate.getMonth() + 1;\n        const usedLeaves = await getUserMonthlyLeaveCount(leaveData.userId, currentYear, currentMonth);\n        const maxLeaves = 4 // Monthly leave quota\n        ;\n        // Determine status and approval details\n        let status = 'pending';\n        let reviewedBy;\n        let reviewedAt = undefined;\n        let reviewNotes;\n        // Auto-approve if user has available quota\n        if (usedLeaves < maxLeaves) {\n            status = 'approved';\n            reviewedBy = 'system';\n            reviewedAt = firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now();\n            reviewNotes = `Auto-approved: ${usedLeaves + 1}/${maxLeaves} monthly leaves used`;\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            status,\n            appliedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            ...reviewedBy && {\n                reviewedBy\n            },\n            ...reviewedAt && {\n                reviewedAt\n            },\n            ...reviewNotes && {\n                reviewNotes\n            }\n        });\n        return {\n            id: docRef.id,\n            autoApproved: status === 'approved',\n            usedLeaves: usedLeaves + (status === 'approved' ? 1 : 0),\n            maxLeaves\n        };\n    } catch (error) {\n        console.error('Error applying user leave:', error);\n        throw error;\n    }\n}\nasync function getUserLeaves(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: doc.data().reviewedAt?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user leaves:', error);\n        throw error;\n    }\n}\n// Get all user leaves for admin review\nasync function getAllUserLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('appliedAt', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: doc.data().reviewedAt?.toDate()\n            }));\n        // Get user details for each leave\n        const { getUserData } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\"));\n        for (const leave of leaves){\n            try {\n                const userData = await getUserData(leave.userId);\n                if (userData) {\n                    leave.userName = userData.name;\n                    leave.userEmail = userData.email;\n                }\n            } catch (error) {\n                console.error(`Error getting user data for ${leave.userId}:`, error);\n                leave.userName = 'Unknown User';\n                leave.userEmail = '<EMAIL>';\n            }\n        }\n        return leaves;\n    } catch (error) {\n        console.error('Error getting all user leaves:', error);\n        throw error;\n    }\n}\nasync function updateUserLeaveStatus(leaveId, status, reviewedBy, reviewNotes) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId), {\n            status,\n            reviewedBy,\n            reviewedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            reviewNotes: reviewNotes || ''\n        });\n    } catch (error) {\n        console.error('Error updating user leave status:', error);\n        throw error;\n    }\n}\nasync function cancelUserLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId));\n    } catch (error) {\n        console.error('Error cancelling user leave:', error);\n        throw error;\n    }\n}\nasync function getUserMonthlyLeaveCount(userId, year, month) {\n    try {\n        const startOfMonth = new Date(year, month - 1, 1);\n        const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfMonth)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfMonth)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.size;\n    } catch (error) {\n        console.error('Error getting user monthly leave count:', error);\n        return 0;\n    }\n}\nasync function isUserOnLeave(userId, date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking user leave for user:', userId, 'on date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasUserLeave = !querySnapshot.empty;\n        if (hasUserLeave) {\n            console.log('👤 Found user leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('👤 No user leaves found for today');\n        }\n        return hasUserLeave;\n    } catch (error) {\n        console.error('❌ Error checking user leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// Legacy function - now redirects to centralized calculation\n// This function is kept for backward compatibility but should not be used for new code\nasync function calculateActiveDays(userId, planActivatedDate) {\n    console.warn('⚠️ Using legacy calculateActiveDays function. Please use calculateUserActiveDays from dataService instead.');\n    // Import and use the centralized calculation\n    const { calculateUserActiveDays } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\"));\n    return await calculateUserActiveDays(userId);\n}\n// Check if work/withdrawals should be blocked\nasync function isWorkBlocked(userId) {\n    try {\n        const today = new Date();\n        console.log('🔍 Checking work block status for user:', userId, 'on date:', today.toDateString());\n        // Check admin leave with detailed logging\n        try {\n            const isAdminLeave = await isAdminLeaveDay(today);\n            console.log('📅 Admin leave check result:', isAdminLeave);\n            if (isAdminLeave) {\n                console.log('🚫 Work blocked due to admin leave');\n                return {\n                    blocked: true,\n                    reason: 'System maintenance/holiday'\n                };\n            }\n        } catch (adminLeaveError) {\n            console.error('❌ Error checking admin leave (allowing work to continue):', adminLeaveError);\n        // Don't block work if admin leave check fails\n        }\n        // Check user leave with detailed logging\n        try {\n            const isUserLeave = await isUserOnLeave(userId, today);\n            console.log('👤 User leave check result:', isUserLeave);\n            if (isUserLeave) {\n                console.log('🚫 Work blocked due to user leave');\n                return {\n                    blocked: true,\n                    reason: 'You are on approved leave today'\n                };\n            }\n        } catch (userLeaveError) {\n            console.error('❌ Error checking user leave (allowing work to continue):', userLeaveError);\n        // Don't block work if user leave check fails\n        }\n        console.log('✅ Work is not blocked');\n        return {\n            blocked: false\n        };\n    } catch (error) {\n        console.error('❌ Error checking work block status (allowing work to continue):', error);\n        return {\n            blocked: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/leaveService.ts\n");

/***/ })

};
;