"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./src/app/wallet/page.tsx":
/*!*********************************!*\
  !*** ./src/app/wallet/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _lib_optimizedDataService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/optimizedDataService */ \"(app-pages-browser)/./src/lib/optimizedDataService.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    const [walletData, setWalletData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWithdrawing, setIsWithdrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Bank details state\n    const [bankDetails, setBankDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBankForm, setShowBankForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bankFormData, setBankFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        accountHolderName: '',\n        accountNumber: '',\n        ifscCode: '',\n        bankName: ''\n    });\n    const [isSavingBank, setIsSavingBank] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalAllowed, setWithdrawalAllowed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        allowed: true\n    });\n    const [checkingWithdrawal, setCheckingWithdrawal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            // Trigger daily process check on wallet page load\n            const triggerDailyProcess = {\n                \"WalletPage.useEffect.triggerDailyProcess\": async ()=>{\n                    try {\n                        const { checkAndRunDailyProcess } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n                        await checkAndRunDailyProcess();\n                    } catch (error) {\n                        console.error('Daily process trigger failed:', error);\n                    }\n                }\n            }[\"WalletPage.useEffect.triggerDailyProcess\"];\n            triggerDailyProcess();\n            if (user) {\n                loadWalletData();\n                loadBankDetails();\n                loadUserData();\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        user\n    ]);\n    // Monitor leave status changes and update withdrawal eligibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (isLeaveBlocked) {\n                setWithdrawalAllowed({\n                    allowed: false,\n                    reason: leaveStatus.reason || 'Withdrawals are not available due to leave.'\n                });\n            } else if (user) {\n                // Re-check withdrawal eligibility when leave status changes\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        isLeaveBlocked,\n        leaveStatus,\n        user\n    ]);\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const checkWithdrawalEligibility = async ()=>{\n        if (!user) return;\n        try {\n            setCheckingWithdrawal(true);\n            const result = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.checkWithdrawalAllowed)(user.uid);\n            setWithdrawalAllowed(result);\n        } catch (error) {\n            console.error('Error checking withdrawal eligibility:', error);\n            setWithdrawalAllowed({\n                allowed: false,\n                reason: 'Unable to verify withdrawal eligibility. Please try again.'\n            });\n        } finally{\n            setCheckingWithdrawal(false);\n        }\n    };\n    const loadWalletData = async ()=>{\n        try {\n            setDataLoading(true);\n            // Use optimized functions for better performance\n            try {\n                // Temporarily force fallback to test\n                throw new Error('Testing fallback method');\n                console.log('🚀 Loading wallet data with optimized functions...');\n                const [walletResult, transactionsResult] = await Promise.all([\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                    _lib_optimizedDataService__WEBPACK_IMPORTED_MODULE_7__.optimizedService.getUserTransactions(user.uid, 20, 'withdrawal_request')\n                ]);\n                setWalletData(walletResult);\n                // Convert transactions to expected format\n                const formattedTransactions = transactionsResult.map((transaction)=>({\n                        id: transaction.id,\n                        type: transaction.type,\n                        amount: transaction.type === 'withdrawal_request' ? -Math.abs(transaction.amount) : transaction.amount,\n                        description: transaction.description,\n                        date: transaction.date,\n                        status: transaction.status || 'completed'\n                    }));\n                setTransactions(formattedTransactions);\n                console.log('✅ Wallet data loaded via optimized functions', formattedTransactions);\n            } catch (optimizedError) {\n                console.warn('⚠️ Optimized functions failed, using fallback:', optimizedError);\n                // Fallback to original method\n                const [walletResult, withdrawalsResult] = await Promise.all([\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserWithdrawals)(user.uid, 20)\n                ]);\n                setWalletData(walletResult);\n                // Convert withdrawals to transaction format for display\n                const withdrawalTransactions = withdrawalsResult.map((withdrawal)=>({\n                        id: withdrawal.id,\n                        type: 'withdrawal',\n                        amount: -withdrawal.amount,\n                        description: \"Withdrawal request - ₹\".concat(withdrawal.amount),\n                        date: withdrawal.date,\n                        status: withdrawal.status\n                    }));\n                setTransactions(withdrawalTransactions);\n                console.log('✅ Wallet data loaded via fallback method', withdrawalTransactions);\n            }\n        } catch (error) {\n            console.error('Error loading wallet data:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: 'Failed to load wallet data. Please try again.'\n            });\n        } finally{\n            setDataLoading(false);\n        }\n    };\n    const loadBankDetails = async ()=>{\n        try {\n            const bankData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getBankDetails)(user.uid);\n            setBankDetails(bankData);\n            if (bankData) {\n                setBankFormData(bankData);\n            }\n        } catch (error) {\n            console.error('Error loading bank details:', error);\n        }\n    };\n    const handleBankFormSubmit = async (e)=>{\n        e.preventDefault();\n        // Prevent multiple submissions\n        if (isSavingBank) return;\n        try {\n            setIsSavingBank(true);\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.saveBankDetails)(user.uid, bankFormData);\n            setBankDetails(bankFormData);\n            setShowBankForm(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Bank Details Saved',\n                text: 'Your bank details have been saved successfully',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } catch (error) {\n            console.error('Error saving bank details:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: error.message || 'Failed to save bank details. Please try again.'\n            });\n        } finally{\n            setIsSavingBank(false);\n        }\n    };\n    const handleBankFormChange = (e)=>{\n        const { name, value } = e.target;\n        setBankFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleWithdraw = async ()=>{\n        // Prevent multiple clicks with strict checking\n        if (isWithdrawing) {\n            console.log('⚠️ Withdrawal already in progress, ignoring duplicate request');\n            return;\n        }\n        // Check if plan is expired based on active days\n        try {\n            const { isUserPlanExpired } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n            const planStatus = await isUserPlanExpired(user.uid);\n            if (planStatus.expired) {\n                sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                    icon: 'error',\n                    title: 'Plan Expired',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-2\">Your plan has expired and you cannot make withdrawals.</p>\\n              <p class=\"text-sm text-gray-600 mb-2\">'.concat(planStatus.reason, '</p>\\n              <p class=\"text-sm text-blue-600\">Please upgrade your plan to continue using withdrawal services.</p>\\n            </div>\\n          '),\n                    confirmButtonText: 'Go to Plans',\n                    showCancelButton: true,\n                    cancelButtonText: 'OK'\n                }).then((result)=>{\n                    if (result.isConfirmed) {\n                        window.location.href = '/plans';\n                    }\n                });\n                return;\n            }\n        } catch (error) {\n            console.error('Error checking plan expiry:', error);\n        }\n        // Check if withdrawals are blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'warning',\n                title: 'Withdrawal Not Available',\n                text: leaveStatus.reason || 'Withdrawals are not available due to leave.',\n                confirmButtonText: 'OK'\n            });\n            return;\n        }\n        const amount = parseFloat(withdrawAmount);\n        if (!amount || amount <= 0) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Invalid Amount',\n                text: 'Please enter a valid amount to withdraw'\n            });\n            return;\n        }\n        if (amount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Minimum Withdrawal',\n                text: 'Minimum withdrawal amount is ₹50'\n            });\n            return;\n        }\n        if (amount > ((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Insufficient Balance',\n                text: 'You do not have enough balance in your wallet'\n            });\n            return;\n        }\n        if (!bankDetails) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'warning',\n                title: 'Bank Details Required',\n                text: 'Please add your bank details before making a withdrawal'\n            });\n            return;\n        }\n        try {\n            // Set loading state immediately to prevent race conditions\n            setIsWithdrawing(true);\n            console.log(\"\\uD83D\\uDD04 Processing withdrawal request for ₹\".concat(amount));\n            // Create withdrawal request in Firestore with atomic operations\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.createWithdrawalRequest)(user.uid, amount, bankDetails);\n            // Reload wallet data to show updated transactions\n            await loadWalletData();\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Withdrawal Request Submitted',\n                text: \"Your withdrawal request for ₹\".concat(amount, \" has been submitted and will be processed within 24-48 hours.\")\n            });\n            setWithdrawAmount('');\n            console.log(\"✅ Withdrawal request completed successfully\");\n        } catch (error) {\n            console.error('❌ Error processing withdrawal:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Withdrawal Failed',\n                text: error.message || 'Failed to process withdrawal request. Please try again.'\n            });\n        } finally{\n            setIsWithdrawing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        if (amount === undefined || amount === null || isNaN(amount)) {\n            return '₹0.00';\n        }\n        return \"₹\".concat(amount.toFixed(2));\n    };\n    if (loading || dataLoading || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading wallet...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 358,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 372,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-arrow-left mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"My Wallet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadWalletData,\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sync-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-semibold text-white\",\n                                children: \"My Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-wallet text-green-400 text-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-4xl font-bold text-green-400 mb-2\",\n                        children: formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60\",\n                        children: \"Total available balance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this),\n                            bankDetails && !showBankForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-edit mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails && !showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-university text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"No bank details added yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this) : showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleBankFormSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Holder Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountHolderName\",\n                                                value: bankFormData.accountHolderName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account holder name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Bank Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"bankName\",\n                                                value: bankFormData.bankName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter bank name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Number *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountNumber\",\n                                                value: bankFormData.accountNumber,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account number\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"IFSC Code *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"ifscCode\",\n                                                value: bankFormData.ifscCode,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter IFSC code (e.g., SBIN0001234)\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSavingBank,\n                                        className: \"\".concat(isSavingBank ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:bg-blue-600'),\n                                        children: isSavingBank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving Bank Details...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-save mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Save Bank Details\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowBankForm(false),\n                                        className: \"btn-secondary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Holder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountHolderName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.bankName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: [\n                                                \"****\",\n                                                bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountNumber.slice(-4)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"IFSC Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.ifscCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this),\n                            \"Withdraw Funds\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this),\n                    (userData === null || userData === void 0 ? void 0 : userData.plan) === 'Trial' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-lock text-red-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400 font-medium\",\n                                        children: \"Trial Plan Restriction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-3\",\n                                children: \"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/plans\",\n                                    className: \"btn-primary inline-block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-arrow-up mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock text-blue-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 font-medium\",\n                                        children: \"Withdrawal Timings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-2\",\n                                children: [\n                                    \"Withdrawals are only allowed between \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"10:00 AM to 6:00 PM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 50\n                                    }, this),\n                                    \" on non-leave days.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: [\n                                            \"Current time: \",\n                                            new Date().toLocaleTimeString(),\n                                            \" | Status: \",\n                                            withdrawalAllowed.allowed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-medium\",\n                                                children: \"✓ Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-medium\",\n                                                children: \"✗ Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkWithdrawalEligibility,\n                                        disabled: checkingWithdrawal,\n                                        className: \"text-blue-400 hover:text-blue-300 text-xs\",\n                                        children: checkingWithdrawal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"spinner w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 11\n                            }, this),\n                            !withdrawalAllowed.allowed && withdrawalAllowed.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this),\n                                    withdrawalAllowed.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"Please add your bank details before making a withdrawal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: withdrawAmount,\n                                        onChange: (e)=>setWithdrawAmount(e.target.value),\n                                        placeholder: \"Enter amount to withdraw (Min: ₹50)\",\n                                        className: \"form-input flex-1\",\n                                        min: \"50\",\n                                        max: (walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleWithdraw,\n                                        disabled: isWithdrawing || !withdrawAmount || !withdrawalAllowed.allowed || parseFloat(withdrawAmount) <= 0,\n                                        className: \"whitespace-nowrap \".concat(isWithdrawing ? 'btn-disabled cursor-not-allowed opacity-50 pointer-events-none' : withdrawalAllowed.allowed && withdrawAmount && parseFloat(withdrawAmount) > 0 ? 'btn-primary hover:bg-blue-600' : 'btn-disabled cursor-not-allowed opacity-50'),\n                                        style: {\n                                            pointerEvents: isWithdrawing ? 'none' : 'auto'\n                                        },\n                                        children: isWithdrawing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing Withdrawal...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-download mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Withdraw ₹\",\n                                                withdrawAmount || '0'\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 text-sm mt-2\",\n                                children: [\n                                    \"Available: \",\n                                    formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0),\n                                    \" | Minimum: ₹50\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-money-bill-wave mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Withdrawal History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadWalletData,\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-sync-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-2\",\n                                children: \"No withdrawal requests yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/40 text-sm\",\n                                children: \"Your withdrawal requests will appear here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/10 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-money-bill-wave text-red-400 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: transaction.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: [\n                                                            transaction.date.toLocaleDateString(),\n                                                            \" at \",\n                                                            transaction.date.toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-red-400\",\n                                                children: formatCurrency(Math.abs(transaction.amount))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs px-2 py-1 rounded-full \".concat(transaction.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' : transaction.status === 'approved' ? 'bg-green-500/20 text-green-400' : transaction.status === 'rejected' ? 'bg-red-500/20 text-red-400' : transaction.status === 'completed' ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'),\n                                                children: transaction.status === 'pending' ? '⏳ Pending' : transaction.status === 'approved' ? '✅ Approved' : transaction.status === 'rejected' ? '❌ Rejected' : transaction.status === 'completed' ? '✅ Completed' : transaction.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"VsQSNUzb5ENRMUCOOCNTvy+okCc=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvd2FsbGV0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDZjtBQUNvQjtBQUMyQjtBQUNsQjtBQUN3SDtBQUNwSDtBQUNpQjtBQUNoRDtBQWVmLFNBQVNnQjs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHZiw4REFBY0E7SUFDeEMsTUFBTSxFQUFFZ0Isd0JBQXdCLEVBQUVDLFVBQVUsRUFBRUMsYUFBYSxFQUFFLEdBQUdqQix5RkFBd0JBLENBQUNhLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUssR0FBRyxLQUFJO0lBQ3RHLE1BQU0sRUFBRUMsV0FBV0MsY0FBYyxFQUFFQyxXQUFXLEVBQUUsR0FBR3BCLHVFQUFlQSxDQUFDO1FBQ2pFcUIsUUFBUVQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNSyxHQUFHLEtBQUk7UUFDckJLLGVBQWU7UUFDZkMsU0FBUyxDQUFDLENBQUNYO0lBQ2I7SUFDQSxNQUFNLENBQUNZLFlBQVlDLGNBQWMsR0FBRzlCLCtDQUFRQSxDQUFvQjtJQUNoRSxNQUFNLENBQUMrQixjQUFjQyxnQkFBZ0IsR0FBR2hDLCtDQUFRQSxDQUFnQixFQUFFO0lBQ2xFLE1BQU0sQ0FBQ2lDLGFBQWFDLGVBQWUsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21DLGdCQUFnQkMsa0JBQWtCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNxQyxlQUFlQyxpQkFBaUIsR0FBR3RDLCtDQUFRQSxDQUFDO0lBRW5ELHFCQUFxQjtJQUNyQixNQUFNLENBQUN1QyxhQUFhQyxlQUFlLEdBQUd4QywrQ0FBUUEsQ0FBcUI7SUFDbkUsTUFBTSxDQUFDeUMsY0FBY0MsZ0JBQWdCLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMyQyxjQUFjQyxnQkFBZ0IsR0FBRzVDLCtDQUFRQSxDQUFjO1FBQzVENkMsbUJBQW1CO1FBQ25CQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ21ELG1CQUFtQkMscUJBQXFCLEdBQUdwRCwrQ0FBUUEsQ0FBd0M7UUFBRXFELFNBQVM7SUFBSztJQUNsSCxNQUFNLENBQUNDLG9CQUFvQkMsc0JBQXNCLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUN3RCxVQUFVQyxZQUFZLEdBQUd6RCwrQ0FBUUEsQ0FBTTtJQUU5Q0MsZ0RBQVNBO2dDQUFDO1lBQ1Isa0RBQWtEO1lBQ2xELE1BQU15RDs0REFBc0I7b0JBQzFCLElBQUk7d0JBQ0YsTUFBTSxFQUFFQyx1QkFBdUIsRUFBRSxHQUFHLE1BQU0sNkpBQTJCO3dCQUNyRSxNQUFNQTtvQkFDUixFQUFFLE9BQU9DLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO29CQUNqRDtnQkFDRjs7WUFDQUY7WUFFQSxJQUFJekMsTUFBTTtnQkFDUjZDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO1lBQ0Y7UUFDRjsrQkFBRztRQUFDaEQ7S0FBSztJQUVULGlFQUFpRTtJQUNqRWhCLGdEQUFTQTtnQ0FBQztZQUNSLElBQUl1QixnQkFBZ0I7Z0JBQ2xCNEIscUJBQXFCO29CQUNuQkMsU0FBUztvQkFDVGEsUUFBUXpDLFlBQVl5QyxNQUFNLElBQUk7Z0JBQ2hDO1lBQ0YsT0FBTyxJQUFJakQsTUFBTTtnQkFDZiw0REFBNEQ7Z0JBQzVEZ0Q7WUFDRjtRQUNGOytCQUFHO1FBQUN6QztRQUFnQkM7UUFBYVI7S0FBSztJQUV0QyxNQUFNK0MsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTUcsT0FBTyxNQUFNdkQsNkRBQVdBLENBQUNLLEtBQU1LLEdBQUc7WUFDeENtQyxZQUFZVTtRQUNkLEVBQUUsT0FBT1AsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtRQUM1QztJQUNGO0lBRUEsTUFBTUssNkJBQTZCO1FBQ2pDLElBQUksQ0FBQ2hELE1BQU07UUFFWCxJQUFJO1lBQ0ZzQyxzQkFBc0I7WUFDdEIsTUFBTWEsU0FBUyxNQUFNekQsd0VBQXNCQSxDQUFDTSxLQUFLSyxHQUFHO1lBQ3BEOEIscUJBQXFCZ0I7UUFDdkIsRUFBRSxPQUFPUixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1lBQ3hEUixxQkFBcUI7Z0JBQ25CQyxTQUFTO2dCQUNUYSxRQUFRO1lBQ1Y7UUFDRixTQUFVO1lBQ1JYLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsTUFBTU8saUJBQWlCO1FBQ3JCLElBQUk7WUFDRjVCLGVBQWU7WUFFZixpREFBaUQ7WUFDakQsSUFBSTtnQkFDRixxQ0FBcUM7Z0JBQ3JDLE1BQU0sSUFBSW1DLE1BQU07Z0JBRWhCUixRQUFRUyxHQUFHLENBQUM7Z0JBQ1osTUFBTSxDQUFDQyxjQUFjQyxtQkFBbUIsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7b0JBQzNEcEUsK0RBQWFBLENBQUNXLEtBQU1LLEdBQUc7b0JBQ3ZCVCx1RUFBZ0JBLENBQUM4RCxtQkFBbUIsQ0FBQzFELEtBQU1LLEdBQUcsRUFBRSxJQUFJO2lCQUNyRDtnQkFFRFEsY0FBY3lDO2dCQUVkLDBDQUEwQztnQkFDMUMsTUFBTUssd0JBQXdCSixtQkFBbUJLLEdBQUcsQ0FBQyxDQUFDQyxjQUFzQjt3QkFDMUVDLElBQUlELFlBQVlDLEVBQUU7d0JBQ2xCQyxNQUFNRixZQUFZRSxJQUFJO3dCQUN0QkMsUUFBUUgsWUFBWUUsSUFBSSxLQUFLLHVCQUF1QixDQUFDRSxLQUFLQyxHQUFHLENBQUNMLFlBQVlHLE1BQU0sSUFBSUgsWUFBWUcsTUFBTTt3QkFDdEdHLGFBQWFOLFlBQVlNLFdBQVc7d0JBQ3BDQyxNQUFNUCxZQUFZTyxJQUFJO3dCQUN0QkMsUUFBUVIsWUFBWVEsTUFBTSxJQUFJO29CQUNoQztnQkFFQXRELGdCQUFnQjRDO2dCQUNoQmYsUUFBUVMsR0FBRyxDQUFDLGdEQUFnRE07WUFFOUQsRUFBRSxPQUFPVyxnQkFBZ0I7Z0JBQ3ZCMUIsUUFBUTJCLElBQUksQ0FBQyxrREFBa0REO2dCQUUvRCw4QkFBOEI7Z0JBQzlCLE1BQU0sQ0FBQ2hCLGNBQWNrQixrQkFBa0IsR0FBRyxNQUFNaEIsUUFBUUMsR0FBRyxDQUFDO29CQUMxRHBFLCtEQUFhQSxDQUFDVyxLQUFNSyxHQUFHO29CQUN2Qlosb0VBQWtCQSxDQUFDTyxLQUFNSyxHQUFHLEVBQUU7aUJBQy9CO2dCQUVEUSxjQUFjeUM7Z0JBQ2Qsd0RBQXdEO2dCQUN4RCxNQUFNbUIseUJBQXlCRCxrQkFBa0JaLEdBQUcsQ0FBQyxDQUFDYyxhQUFxQjt3QkFDekVaLElBQUlZLFdBQVdaLEVBQUU7d0JBQ2pCQyxNQUFNO3dCQUNOQyxRQUFRLENBQUNVLFdBQVdWLE1BQU07d0JBQzFCRyxhQUFhLHlCQUEyQyxPQUFsQk8sV0FBV1YsTUFBTTt3QkFDdkRJLE1BQU1NLFdBQVdOLElBQUk7d0JBQ3JCQyxRQUFRSyxXQUFXTCxNQUFNO29CQUMzQjtnQkFDQXRELGdCQUFnQjBEO2dCQUNoQjdCLFFBQVFTLEdBQUcsQ0FBQyw0Q0FBNENvQjtZQUMxRDtRQUNGLEVBQUUsT0FBTzlCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUM3Qyx1REFBUyxDQUFDO2dCQUNSOEUsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsTUFBTTtZQUNSO1FBQ0YsU0FBVTtZQUNSN0QsZUFBZTtRQUNqQjtJQUNGO0lBRUEsTUFBTTZCLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTWlDLFdBQVcsTUFBTXpGLGdFQUFjQSxDQUFDVSxLQUFNSyxHQUFHO1lBQy9Da0IsZUFBZXdEO1lBQ2YsSUFBSUEsVUFBVTtnQkFDWnBELGdCQUFnQm9EO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPcEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUMvQztJQUNGO0lBRUEsTUFBTXFDLHVCQUF1QixPQUFPQztRQUNsQ0EsRUFBRUMsY0FBYztRQUVoQiwrQkFBK0I7UUFDL0IsSUFBSWxELGNBQWM7UUFFbEIsSUFBSTtZQUNGQyxnQkFBZ0I7WUFDaEIsTUFBTTFDLGlFQUFlQSxDQUFDUyxLQUFNSyxHQUFHLEVBQUVxQjtZQUVqQ0gsZUFBZUc7WUFDZkQsZ0JBQWdCO1lBRWhCM0IsdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05LLE9BQU87Z0JBQ1BDLG1CQUFtQjtZQUNyQjtRQUNGLEVBQUUsT0FBT3pDLE9BQVk7WUFDbkJDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDN0MsdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU1uQyxNQUFNMEMsT0FBTyxJQUFJO1lBQ3pCO1FBQ0YsU0FBVTtZQUNScEQsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNcUQsdUJBQXVCLENBQUNMO1FBQzVCLE1BQU0sRUFBRU0sSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR1AsRUFBRVEsTUFBTTtRQUNoQzlELGdCQUFnQitELENBQUFBLE9BQVM7Z0JBQ3ZCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0gsS0FBSyxFQUFFQztZQUNWO0lBQ0Y7SUFFQSxNQUFNRyxpQkFBaUI7UUFDckIsK0NBQStDO1FBQy9DLElBQUl2RSxlQUFlO1lBQ2pCd0IsUUFBUVMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLGdEQUFnRDtRQUNoRCxJQUFJO1lBQ0YsTUFBTSxFQUFFdUMsaUJBQWlCLEVBQUUsR0FBRyxNQUFNLDZKQUEyQjtZQUMvRCxNQUFNQyxhQUFhLE1BQU1ELGtCQUFrQjVGLEtBQU1LLEdBQUc7WUFFcEQsSUFBSXdGLFdBQVdDLE9BQU8sRUFBRTtnQkFDdEJoRyx1REFBUyxDQUFDO29CQUNSOEUsTUFBTTtvQkFDTkMsT0FBTztvQkFDUGtCLE1BQU0sMExBR3dELE9BQWxCRixXQUFXNUMsTUFBTSxFQUFDO29CQUk5RCtDLG1CQUFtQjtvQkFDbkJDLGtCQUFrQjtvQkFDbEJDLGtCQUFrQjtnQkFDcEIsR0FBR0MsSUFBSSxDQUFDLENBQUNoRDtvQkFDUCxJQUFJQSxPQUFPaUQsV0FBVyxFQUFFO3dCQUN0QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7b0JBQ3pCO2dCQUNGO2dCQUNBO1lBQ0Y7UUFDRixFQUFFLE9BQU81RCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUlwQyxnQkFBZ0I7WUFDbEJULHVEQUFTLENBQUM7Z0JBQ1I4RSxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxNQUFNdEUsWUFBWXlDLE1BQU0sSUFBSTtnQkFDNUIrQyxtQkFBbUI7WUFDckI7WUFDQTtRQUNGO1FBRUEsTUFBTWhDLFNBQVN3QyxXQUFXdEY7UUFFMUIsSUFBSSxDQUFDOEMsVUFBVUEsVUFBVSxHQUFHO1lBQzFCbEUsdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQSxJQUFJZCxTQUFTLElBQUk7WUFDZmxFLHVEQUFTLENBQUM7Z0JBQ1I4RSxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxNQUFNO1lBQ1I7WUFDQTtRQUNGO1FBRUEsSUFBSWQsU0FBVXBELENBQUFBLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWTZGLE1BQU0sS0FBSSxJQUFJO1lBQ3RDM0csdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQSxJQUFJLENBQUN4RCxhQUFhO1lBQ2hCeEIsdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsMkRBQTJEO1lBQzNEekQsaUJBQWlCO1lBQ2pCdUIsUUFBUVMsR0FBRyxDQUFDLG1EQUFnRCxPQUFQVztZQUVyRCxnRUFBZ0U7WUFDaEUsTUFBTXhFLHlFQUF1QkEsQ0FBQ1EsS0FBTUssR0FBRyxFQUFFMkQsUUFBUTFDO1lBRWpELGtEQUFrRDtZQUNsRCxNQUFNdUI7WUFFTi9DLHVEQUFTLENBQUM7Z0JBQ1I4RSxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxNQUFNLGdDQUF1QyxPQUFQZCxRQUFPO1lBQy9DO1lBRUE3QyxrQkFBa0I7WUFDbEJ5QixRQUFRUyxHQUFHLENBQUU7UUFDZixFQUFFLE9BQU9WLE9BQVk7WUFDbkJDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hEN0MsdURBQVMsQ0FBQztnQkFDUjhFLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU1uQyxNQUFNMEMsT0FBTyxJQUFJO1lBQ3pCO1FBQ0YsU0FBVTtZQUNSaEUsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNcUYsaUJBQWlCLENBQUMxQztRQUN0QixJQUFJQSxXQUFXMkMsYUFBYTNDLFdBQVcsUUFBUTRDLE1BQU01QyxTQUFTO1lBQzVELE9BQU87UUFDVDtRQUNBLE9BQU8sSUFBc0IsT0FBbEJBLE9BQU82QyxPQUFPLENBQUM7SUFDNUI7SUFJQSxJQUFJNUcsV0FBV2UsZUFBZWIsWUFBWTtRQUN4QyxxQkFDRSw4REFBQzJHO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQ1Y5RyxVQUFVLGVBQWVFLGFBQWEsOEJBQThCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUsvRTtJQUVBLDJDQUEyQztJQUMzQyxJQUFJRCw0QkFBNEJGLE1BQU07UUFDcEMscUJBQ0UsOERBQUNILDZFQUF5QkE7WUFDeEJZLFFBQVFULEtBQUtLLEdBQUc7WUFDaEI0RyxXQUFXN0c7Ozs7OztJQUdqQjtJQUVBLHFCQUNFLDhEQUFDMEc7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNHO2dCQUFPSCxXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDOUgsa0RBQUlBOzRCQUFDc0gsTUFBSzs0QkFBYVEsV0FBVTs7OENBQ2hDLDhEQUFDSTtvQ0FBRUosV0FBVTs7Ozs7O2dDQUE2Qjs7Ozs7OztzQ0FHNUMsOERBQUNLOzRCQUFHTCxXQUFVO3NDQUErQjs7Ozs7O3NDQUM3Qyw4REFBQ007NEJBQ0NDLFNBQVN6RTs0QkFDVGtFLFdBQVU7OzhDQUVWLDhEQUFDSTtvQ0FBRUosV0FBVTs7Ozs7O2dDQUEyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU85Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOzBDQUFvQzs7Ozs7OzBDQUNsRCw4REFBQ0k7Z0NBQUVKLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FFZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQ1ZMLGVBQWU5RixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVk2RixNQUFNLEtBQUk7Ozs7OztrQ0FFeEMsOERBQUNPO3dCQUFFRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7OzBCQUkvQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOztrREFDWiw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztvQ0FBNkI7Ozs7Ozs7NEJBRzNDekYsZUFBZSxDQUFDRSw4QkFDZiw4REFBQzZGO2dDQUNDQyxTQUFTLElBQU03RixnQkFBZ0I7Z0NBQy9Cc0YsV0FBVTs7a0RBRVYsOERBQUNJO3dDQUFFSixXQUFVOzs7Ozs7b0NBQXVCOzs7Ozs7Ozs7Ozs7O29CQU16QyxDQUFDekYsZUFBZSxDQUFDRSw2QkFDaEIsOERBQUNzRjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNJO2dDQUFFSixXQUFVOzs7Ozs7MENBQ2IsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFxQjs7Ozs7OzBDQUNsQyw4REFBQ007Z0NBQ0NDLFNBQVMsSUFBTTdGLGdCQUFnQjtnQ0FDL0JzRixXQUFVOztrREFFViw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztvQ0FBdUI7Ozs7Ozs7Ozs7OzsrQkFJdEN2Riw2QkFDRiw4REFBQ2dHO3dCQUFLQyxVQUFVekM7d0JBQXNCK0IsV0FBVTs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ1k7Z0RBQU1YLFdBQVU7MERBQW9DOzs7Ozs7MERBR3JELDhEQUFDWTtnREFDQzVELE1BQUs7Z0RBQ0x3QixNQUFLO2dEQUNMQyxPQUFPOUQsYUFBYUUsaUJBQWlCO2dEQUNyQ2dHLFVBQVV0QztnREFDVnlCLFdBQVU7Z0RBQ1ZjLGFBQVk7Z0RBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztrREFHWiw4REFBQ2hCOzswREFDQyw4REFBQ1k7Z0RBQU1YLFdBQVU7MERBQW9DOzs7Ozs7MERBR3JELDhEQUFDWTtnREFDQzVELE1BQUs7Z0RBQ0x3QixNQUFLO2dEQUNMQyxPQUFPOUQsYUFBYUssUUFBUTtnREFDNUI2RixVQUFVdEM7Z0RBQ1Z5QixXQUFVO2dEQUNWYyxhQUFZO2dEQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7a0RBR1osOERBQUNoQjs7MERBQ0MsOERBQUNZO2dEQUFNWCxXQUFVOzBEQUFvQzs7Ozs7OzBEQUdyRCw4REFBQ1k7Z0RBQ0M1RCxNQUFLO2dEQUNMd0IsTUFBSztnREFDTEMsT0FBTzlELGFBQWFHLGFBQWE7Z0RBQ2pDK0YsVUFBVXRDO2dEQUNWeUIsV0FBVTtnREFDVmMsYUFBWTtnREFDWkMsUUFBUTs7Ozs7Ozs7Ozs7O2tEQUdaLDhEQUFDaEI7OzBEQUNDLDhEQUFDWTtnREFBTVgsV0FBVTswREFBb0M7Ozs7OzswREFHckQsOERBQUNZO2dEQUNDNUQsTUFBSztnREFDTHdCLE1BQUs7Z0RBQ0xDLE9BQU85RCxhQUFhSSxRQUFRO2dEQUM1QjhGLFVBQVV0QztnREFDVnlCLFdBQVU7Z0RBQ1ZjLGFBQVk7Z0RBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJZCw4REFBQ2hCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ007d0NBQ0N0RCxNQUFLO3dDQUNMZ0UsVUFBVS9GO3dDQUNWK0UsV0FBVyxHQUFpRyxPQUE5Ri9FLGVBQWUsK0NBQStDO2tEQUUzRUEsNkJBQ0M7OzhEQUNFLDhEQUFDOEU7b0RBQUlDLFdBQVU7Ozs7OztnREFBNkI7O3lFQUk5Qzs7OERBQ0UsOERBQUNJO29EQUFFSixXQUFVOzs7Ozs7Z0RBQXVCOzs7Ozs7OztrREFLMUMsOERBQUNNO3dDQUNDdEQsTUFBSzt3Q0FDTHVELFNBQVMsSUFBTTdGLGdCQUFnQjt3Q0FDL0JzRixXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs2Q0FNTCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDRTs0Q0FBRUQsV0FBVTtzREFBd0I7Ozs7OztzREFDckMsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUEwQnpGLHdCQUFBQSxrQ0FBQUEsWUFBYU0saUJBQWlCOzs7Ozs7Ozs7Ozs7OENBRXZFLDhEQUFDa0Y7O3NEQUNDLDhEQUFDRTs0Q0FBRUQsV0FBVTtzREFBd0I7Ozs7OztzREFDckMsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUEwQnpGLHdCQUFBQSxrQ0FBQUEsWUFBYVMsUUFBUTs7Ozs7Ozs7Ozs7OzhDQUU5RCw4REFBQytFOztzREFDQyw4REFBQ0U7NENBQUVELFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDQzs0Q0FBRUQsV0FBVTs7Z0RBQXlCO2dEQUMvQnpGLHdCQUFBQSxrQ0FBQUEsWUFBYU8sYUFBYSxDQUFDbUcsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7OENBRzNDLDhEQUFDbEI7O3NEQUNDLDhEQUFDRTs0Q0FBRUQsV0FBVTtzREFBd0I7Ozs7OztzREFDckMsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUEwQnpGLHdCQUFBQSxrQ0FBQUEsWUFBYVEsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXRFLDhEQUFDZ0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDUTt3QkFBR1IsV0FBVTs7MENBQ1osOERBQUNJO2dDQUFFSixXQUFVOzs7Ozs7NEJBQWtDOzs7Ozs7O29CQUtoRHhFLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVTBGLElBQUksTUFBSyx5QkFDbEIsOERBQUNuQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztrREFDYiw4REFBQ21CO3dDQUFLbkIsV0FBVTtrREFBMkI7Ozs7Ozs7Ozs7OzswQ0FFN0MsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUE2Qjs7Ozs7OzBDQUcxQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM5SCxrREFBSUE7b0NBQUNzSCxNQUFLO29DQUFTUSxXQUFVOztzREFDNUIsOERBQUNJOzRDQUFFSixXQUFVOzs7Ozs7d0NBQTJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBU2hELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztrREFDYiw4REFBQ21CO3dDQUFLbkIsV0FBVTtrREFBNEI7Ozs7Ozs7Ozs7OzswQ0FFOUMsOERBQUNDO2dDQUFFRCxXQUFVOztvQ0FBNkI7a0RBQ0gsOERBQUNvQjtrREFBTzs7Ozs7O29DQUE0Qjs7Ozs7OzswQ0FFM0UsOERBQUNyQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFFRCxXQUFVOzs0Q0FBd0I7NENBQ3BCLElBQUlxQixPQUFPQyxrQkFBa0I7NENBQUc7NENBQ3RDbkcsa0JBQWtCRSxPQUFPLGlCQUNoQyw4REFBQzhGO2dEQUFLbkIsV0FBVTswREFBNkI7Ozs7O3FFQUU3Qyw4REFBQ21CO2dEQUFLbkIsV0FBVTswREFBMkI7Ozs7Ozs7Ozs7OztrREFHL0MsOERBQUNNO3dDQUNDQyxTQUFTdEU7d0NBQ1QrRSxVQUFVMUY7d0NBQ1YwRSxXQUFVO2tEQUVUMUUsbUNBQ0MsOERBQUN5RTs0Q0FBSUMsV0FBVTs7Ozs7aUVBRWYsOERBQUNJOzRDQUFFSixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFJbEIsQ0FBQzdFLGtCQUFrQkUsT0FBTyxJQUFJRixrQkFBa0JlLE1BQU0sa0JBQ3JELDhEQUFDK0Q7Z0NBQUVELFdBQVU7O2tEQUNYLDhEQUFDSTt3Q0FBRUosV0FBVTs7Ozs7O29DQUNaN0Usa0JBQWtCZSxNQUFNOzs7Ozs7Ozs7Ozs7O29CQUs5QixDQUFDM0IsNEJBQ0EsOERBQUN3Rjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNJO2dDQUFFSixXQUFVOzs7Ozs7MENBQ2IsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFxQjs7Ozs7OzBDQUNsQyw4REFBQ007Z0NBQ0NDLFNBQVMsSUFBTTdGLGdCQUFnQjtnQ0FDL0JzRixXQUFVOztrREFFViw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztvQ0FBNkI7Ozs7Ozs7Ozs7Ozs2Q0FLOUM7OzBDQUNFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUNDNUQsTUFBSzt3Q0FDTHlCLE9BQU90RTt3Q0FDUDBHLFVBQVUsQ0FBQzNDLElBQU05RCxrQkFBa0I4RCxFQUFFUSxNQUFNLENBQUNELEtBQUs7d0NBQ2pEcUMsYUFBWTt3Q0FDWmQsV0FBVTt3Q0FDVnVCLEtBQUk7d0NBQ0pDLEtBQUszSCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVk2RixNQUFNLEtBQUk7Ozs7OztrREFFN0IsOERBQUNZO3dDQUNDQyxTQUFTM0I7d0NBQ1RvQyxVQUFVM0csaUJBQWlCLENBQUNGLGtCQUFrQixDQUFDZ0Isa0JBQWtCRSxPQUFPLElBQUlvRSxXQUFXdEYsbUJBQW1CO3dDQUMxRzZGLFdBQVcscUJBTVYsT0FMQzNGLGdCQUNJLG1FQUNBYyxrQkFBa0JFLE9BQU8sSUFBSWxCLGtCQUFrQnNGLFdBQVd0RixrQkFBa0IsSUFDMUUsa0NBQ0E7d0NBRVJzSCxPQUFPOzRDQUFFQyxlQUFlckgsZ0JBQWdCLFNBQVM7d0NBQU87a0RBRXZEQSw4QkFDQzs7OERBQ0UsOERBQUMwRjtvREFBSUMsV0FBVTs7Ozs7O2dEQUE2Qjs7eUVBSTlDOzs4REFDRSw4REFBQ0k7b0RBQUVKLFdBQVU7Ozs7OztnREFBMkI7Z0RBQzdCN0Ysa0JBQWtCOzs7Ozs7Ozs7Ozs7OzswQ0FLckMsOERBQUM4RjtnQ0FBRUQsV0FBVTs7b0NBQTZCO29DQUM1QkwsZUFBZTlGLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWTZGLE1BQU0sS0FBSTtvQ0FBRzs7Ozs7Ozs7Ozs7Ozs7OzBCQU81RCw4REFBQ0s7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOztrREFDWiw4REFBQ0k7d0NBQUVKLFdBQVU7Ozs7OztvQ0FBa0M7Ozs7Ozs7MENBR2pELDhEQUFDTTtnQ0FDQ0MsU0FBU3pFO2dDQUNUa0UsV0FBVTs7a0RBRVYsOERBQUNJO3dDQUFFSixXQUFVOzs7Ozs7b0NBQTJCOzs7Ozs7Ozs7Ozs7O29CQUszQ2pHLGFBQWE0SCxNQUFNLEtBQUssa0JBQ3ZCLDhEQUFDNUI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSTtnQ0FBRUosV0FBVTs7Ozs7OzBDQUNiLDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBcUI7Ozs7OzswQ0FDbEMsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7NkNBR3ZDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWmpHLGFBQWE4QyxHQUFHLENBQUMsQ0FBQ0MsNEJBQ2pCLDhEQUFDaUQ7Z0NBRUNDLFdBQVU7O2tEQUVWLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNJO2dEQUFFSixXQUFVOzs7Ozs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ0U7d0RBQUVELFdBQVU7a0VBQTBCbEQsWUFBWU0sV0FBVzs7Ozs7O2tFQUM5RCw4REFBQzZDO3dEQUFFRCxXQUFVOzs0REFDVmxELFlBQVlPLElBQUksQ0FBQ3VFLGtCQUFrQjs0REFBRzs0REFBSzlFLFlBQVlPLElBQUksQ0FBQ2lFLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJckYsOERBQUN2Qjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUNWTCxlQUFlekMsS0FBS0MsR0FBRyxDQUFDTCxZQUFZRyxNQUFNOzs7Ozs7MERBRTdDLDhEQUFDa0U7Z0RBQUtuQixXQUFXLGtDQU1oQixPQUxDbEQsWUFBWVEsTUFBTSxLQUFLLFlBQVkscUNBQ25DUixZQUFZUSxNQUFNLEtBQUssYUFBYSxtQ0FDcENSLFlBQVlRLE1BQU0sS0FBSyxhQUFhLCtCQUNwQ1IsWUFBWVEsTUFBTSxLQUFLLGNBQWMsaUNBQ3JDOzBEQUVDUixZQUFZUSxNQUFNLEtBQUssWUFBWSxjQUNuQ1IsWUFBWVEsTUFBTSxLQUFLLGFBQWEsZUFDcENSLFlBQVlRLE1BQU0sS0FBSyxhQUFhLGVBQ3BDUixZQUFZUSxNQUFNLEtBQUssY0FBYyxnQkFDckNSLFlBQVlRLE1BQU07Ozs7Ozs7Ozs7Ozs7K0JBM0JsQlIsWUFBWUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXVDbkM7R0Exc0J3Qi9EOztRQUNJYiwwREFBY0E7UUFDd0JDLHFGQUF3QkE7UUFDckNDLG1FQUFlQTs7O0tBSDVDVyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxPbmVEcml2ZVxcRGVza3RvcFxcTVkgUFJPSkVDVFNcXE5vZGUgTXl0dWJlXFxzcmNcXGFwcFxcd2FsbGV0XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlUmVxdWlyZUF1dGggfSBmcm9tICdAL2hvb2tzL3VzZUF1dGgnXG5pbXBvcnQgeyB1c2VCbG9ja2luZ05vdGlmaWNhdGlvbnMgfSBmcm9tICdAL2hvb2tzL3VzZUJsb2NraW5nTm90aWZpY2F0aW9ucydcbmltcG9ydCB7IHVzZUxlYXZlTW9uaXRvciB9IGZyb20gJ0AvaG9va3MvdXNlTGVhdmVNb25pdG9yJ1xuaW1wb3J0IHsgZ2V0V2FsbGV0RGF0YSwgZ2V0QmFua0RldGFpbHMsIHNhdmVCYW5rRGV0YWlscywgQmFua0RldGFpbHMsIGNyZWF0ZVdpdGhkcmF3YWxSZXF1ZXN0LCBnZXRVc2VyV2l0aGRyYXdhbHMsIGNoZWNrV2l0aGRyYXdhbEFsbG93ZWQsIGdldFVzZXJEYXRhIH0gZnJvbSAnQC9saWIvZGF0YVNlcnZpY2UnXG5pbXBvcnQgeyBvcHRpbWl6ZWRTZXJ2aWNlIH0gZnJvbSAnQC9saWIvb3B0aW1pemVkRGF0YVNlcnZpY2UnXG5pbXBvcnQgQmxvY2tpbmdOb3RpZmljYXRpb25Nb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvQmxvY2tpbmdOb3RpZmljYXRpb25Nb2RhbCdcbmltcG9ydCBTd2FsIGZyb20gJ3N3ZWV0YWxlcnQyJ1xuXG5pbnRlcmZhY2UgV2FsbGV0RGF0YSB7XG4gIHdhbGxldDogbnVtYmVyXG59XG5cbmludGVyZmFjZSBUcmFuc2FjdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG4gIGFtb3VudDogbnVtYmVyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgZGF0ZTogRGF0ZVxuICBzdGF0dXM6IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBXYWxsZXRQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIGxvYWRpbmcgfSA9IHVzZVJlcXVpcmVBdXRoKClcbiAgY29uc3QgeyBoYXNCbG9ja2luZ05vdGlmaWNhdGlvbnMsIGlzQ2hlY2tpbmcsIG1hcmtBbGxBc1JlYWQgfSA9IHVzZUJsb2NraW5nTm90aWZpY2F0aW9ucyh1c2VyPy51aWQgfHwgbnVsbClcbiAgY29uc3QgeyBpc0Jsb2NrZWQ6IGlzTGVhdmVCbG9ja2VkLCBsZWF2ZVN0YXR1cyB9ID0gdXNlTGVhdmVNb25pdG9yKHtcbiAgICB1c2VySWQ6IHVzZXI/LnVpZCB8fCBudWxsLFxuICAgIGNoZWNrSW50ZXJ2YWw6IDMwMDAwLCAvLyBDaGVjayBldmVyeSAzMCBzZWNvbmRzXG4gICAgZW5hYmxlZDogISF1c2VyXG4gIH0pXG4gIGNvbnN0IFt3YWxsZXREYXRhLCBzZXRXYWxsZXREYXRhXSA9IHVzZVN0YXRlPFdhbGxldERhdGEgfCBudWxsPihudWxsKVxuICBjb25zdCBbdHJhbnNhY3Rpb25zLCBzZXRUcmFuc2FjdGlvbnNdID0gdXNlU3RhdGU8VHJhbnNhY3Rpb25bXT4oW10pXG4gIGNvbnN0IFtkYXRhTG9hZGluZywgc2V0RGF0YUxvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3dpdGhkcmF3QW1vdW50LCBzZXRXaXRoZHJhd0Ftb3VudF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2lzV2l0aGRyYXdpbmcsIHNldElzV2l0aGRyYXdpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gQmFuayBkZXRhaWxzIHN0YXRlXG4gIGNvbnN0IFtiYW5rRGV0YWlscywgc2V0QmFua0RldGFpbHNdID0gdXNlU3RhdGU8QmFua0RldGFpbHMgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2hvd0JhbmtGb3JtLCBzZXRTaG93QmFua0Zvcm1dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtiYW5rRm9ybURhdGEsIHNldEJhbmtGb3JtRGF0YV0gPSB1c2VTdGF0ZTxCYW5rRGV0YWlscz4oe1xuICAgIGFjY291bnRIb2xkZXJOYW1lOiAnJyxcbiAgICBhY2NvdW50TnVtYmVyOiAnJyxcbiAgICBpZnNjQ29kZTogJycsXG4gICAgYmFua05hbWU6ICcnXG4gIH0pXG4gIGNvbnN0IFtpc1NhdmluZ0JhbmssIHNldElzU2F2aW5nQmFua10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3dpdGhkcmF3YWxBbGxvd2VkLCBzZXRXaXRoZHJhd2FsQWxsb3dlZF0gPSB1c2VTdGF0ZTx7IGFsbG93ZWQ6IGJvb2xlYW47IHJlYXNvbj86IHN0cmluZyB9Pih7IGFsbG93ZWQ6IHRydWUgfSlcbiAgY29uc3QgW2NoZWNraW5nV2l0aGRyYXdhbCwgc2V0Q2hlY2tpbmdXaXRoZHJhd2FsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbdXNlckRhdGEsIHNldFVzZXJEYXRhXSA9IHVzZVN0YXRlPGFueT4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFRyaWdnZXIgZGFpbHkgcHJvY2VzcyBjaGVjayBvbiB3YWxsZXQgcGFnZSBsb2FkXG4gICAgY29uc3QgdHJpZ2dlckRhaWx5UHJvY2VzcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgY2hlY2tBbmRSdW5EYWlseVByb2Nlc3MgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvZGF0YVNlcnZpY2UnKVxuICAgICAgICBhd2FpdCBjaGVja0FuZFJ1bkRhaWx5UHJvY2VzcygpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdEYWlseSBwcm9jZXNzIHRyaWdnZXIgZmFpbGVkOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cbiAgICB0cmlnZ2VyRGFpbHlQcm9jZXNzKClcblxuICAgIGlmICh1c2VyKSB7XG4gICAgICBsb2FkV2FsbGV0RGF0YSgpXG4gICAgICBsb2FkQmFua0RldGFpbHMoKVxuICAgICAgbG9hZFVzZXJEYXRhKClcbiAgICAgIGNoZWNrV2l0aGRyYXdhbEVsaWdpYmlsaXR5KClcbiAgICB9XG4gIH0sIFt1c2VyXSlcblxuICAvLyBNb25pdG9yIGxlYXZlIHN0YXR1cyBjaGFuZ2VzIGFuZCB1cGRhdGUgd2l0aGRyYXdhbCBlbGlnaWJpbGl0eVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0xlYXZlQmxvY2tlZCkge1xuICAgICAgc2V0V2l0aGRyYXdhbEFsbG93ZWQoe1xuICAgICAgICBhbGxvd2VkOiBmYWxzZSxcbiAgICAgICAgcmVhc29uOiBsZWF2ZVN0YXR1cy5yZWFzb24gfHwgJ1dpdGhkcmF3YWxzIGFyZSBub3QgYXZhaWxhYmxlIGR1ZSB0byBsZWF2ZS4nXG4gICAgICB9KVxuICAgIH0gZWxzZSBpZiAodXNlcikge1xuICAgICAgLy8gUmUtY2hlY2sgd2l0aGRyYXdhbCBlbGlnaWJpbGl0eSB3aGVuIGxlYXZlIHN0YXR1cyBjaGFuZ2VzXG4gICAgICBjaGVja1dpdGhkcmF3YWxFbGlnaWJpbGl0eSgpXG4gICAgfVxuICB9LCBbaXNMZWF2ZUJsb2NrZWQsIGxlYXZlU3RhdHVzLCB1c2VyXSlcblxuICBjb25zdCBsb2FkVXNlckRhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZXRVc2VyRGF0YSh1c2VyIS51aWQpXG4gICAgICBzZXRVc2VyRGF0YShkYXRhKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHVzZXIgZGF0YTonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCBjaGVja1dpdGhkcmF3YWxFbGlnaWJpbGl0eSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHNldENoZWNraW5nV2l0aGRyYXdhbCh0cnVlKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2hlY2tXaXRoZHJhd2FsQWxsb3dlZCh1c2VyLnVpZClcbiAgICAgIHNldFdpdGhkcmF3YWxBbGxvd2VkKHJlc3VsdClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgd2l0aGRyYXdhbCBlbGlnaWJpbGl0eTonLCBlcnJvcilcbiAgICAgIHNldFdpdGhkcmF3YWxBbGxvd2VkKHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogJ1VuYWJsZSB0byB2ZXJpZnkgd2l0aGRyYXdhbCBlbGlnaWJpbGl0eS4gUGxlYXNlIHRyeSBhZ2Fpbi4nXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRDaGVja2luZ1dpdGhkcmF3YWwoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFdhbGxldERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldERhdGFMb2FkaW5nKHRydWUpXG5cbiAgICAgIC8vIFVzZSBvcHRpbWl6ZWQgZnVuY3Rpb25zIGZvciBiZXR0ZXIgcGVyZm9ybWFuY2VcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFRlbXBvcmFyaWx5IGZvcmNlIGZhbGxiYWNrIHRvIHRlc3RcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdUZXN0aW5nIGZhbGxiYWNrIG1ldGhvZCcpXG5cbiAgICAgICAgY29uc29sZS5sb2coJ/CfmoAgTG9hZGluZyB3YWxsZXQgZGF0YSB3aXRoIG9wdGltaXplZCBmdW5jdGlvbnMuLi4nKVxuICAgICAgICBjb25zdCBbd2FsbGV0UmVzdWx0LCB0cmFuc2FjdGlvbnNSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICAgIGdldFdhbGxldERhdGEodXNlciEudWlkKSxcbiAgICAgICAgICBvcHRpbWl6ZWRTZXJ2aWNlLmdldFVzZXJUcmFuc2FjdGlvbnModXNlciEudWlkLCAyMCwgJ3dpdGhkcmF3YWxfcmVxdWVzdCcpXG4gICAgICAgIF0pXG5cbiAgICAgICAgc2V0V2FsbGV0RGF0YSh3YWxsZXRSZXN1bHQpXG5cbiAgICAgICAgLy8gQ29udmVydCB0cmFuc2FjdGlvbnMgdG8gZXhwZWN0ZWQgZm9ybWF0XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZFRyYW5zYWN0aW9ucyA9IHRyYW5zYWN0aW9uc1Jlc3VsdC5tYXAoKHRyYW5zYWN0aW9uOiBhbnkpID0+ICh7XG4gICAgICAgICAgaWQ6IHRyYW5zYWN0aW9uLmlkLFxuICAgICAgICAgIHR5cGU6IHRyYW5zYWN0aW9uLnR5cGUsXG4gICAgICAgICAgYW1vdW50OiB0cmFuc2FjdGlvbi50eXBlID09PSAnd2l0aGRyYXdhbF9yZXF1ZXN0JyA/IC1NYXRoLmFicyh0cmFuc2FjdGlvbi5hbW91bnQpIDogdHJhbnNhY3Rpb24uYW1vdW50LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiB0cmFuc2FjdGlvbi5kZXNjcmlwdGlvbixcbiAgICAgICAgICBkYXRlOiB0cmFuc2FjdGlvbi5kYXRlLFxuICAgICAgICAgIHN0YXR1czogdHJhbnNhY3Rpb24uc3RhdHVzIHx8ICdjb21wbGV0ZWQnXG4gICAgICAgIH0pKVxuXG4gICAgICAgIHNldFRyYW5zYWN0aW9ucyhmb3JtYXR0ZWRUcmFuc2FjdGlvbnMgYXMgVHJhbnNhY3Rpb25bXSlcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBXYWxsZXQgZGF0YSBsb2FkZWQgdmlhIG9wdGltaXplZCBmdW5jdGlvbnMnLCBmb3JtYXR0ZWRUcmFuc2FjdGlvbnMpXG5cbiAgICAgIH0gY2F0Y2ggKG9wdGltaXplZEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIE9wdGltaXplZCBmdW5jdGlvbnMgZmFpbGVkLCB1c2luZyBmYWxsYmFjazonLCBvcHRpbWl6ZWRFcnJvcilcblxuICAgICAgICAvLyBGYWxsYmFjayB0byBvcmlnaW5hbCBtZXRob2RcbiAgICAgICAgY29uc3QgW3dhbGxldFJlc3VsdCwgd2l0aGRyYXdhbHNSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICAgIGdldFdhbGxldERhdGEodXNlciEudWlkKSxcbiAgICAgICAgICBnZXRVc2VyV2l0aGRyYXdhbHModXNlciEudWlkLCAyMClcbiAgICAgICAgXSlcblxuICAgICAgICBzZXRXYWxsZXREYXRhKHdhbGxldFJlc3VsdClcbiAgICAgICAgLy8gQ29udmVydCB3aXRoZHJhd2FscyB0byB0cmFuc2FjdGlvbiBmb3JtYXQgZm9yIGRpc3BsYXlcbiAgICAgICAgY29uc3Qgd2l0aGRyYXdhbFRyYW5zYWN0aW9ucyA9IHdpdGhkcmF3YWxzUmVzdWx0Lm1hcCgod2l0aGRyYXdhbDogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiB3aXRoZHJhd2FsLmlkLFxuICAgICAgICAgIHR5cGU6ICd3aXRoZHJhd2FsJyxcbiAgICAgICAgICBhbW91bnQ6IC13aXRoZHJhd2FsLmFtb3VudCwgLy8gTmVnYXRpdmUgdG8gc2hvdyBhcyBkZWJpdFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgV2l0aGRyYXdhbCByZXF1ZXN0IC0g4oK5JHt3aXRoZHJhd2FsLmFtb3VudH1gLFxuICAgICAgICAgIGRhdGU6IHdpdGhkcmF3YWwuZGF0ZSxcbiAgICAgICAgICBzdGF0dXM6IHdpdGhkcmF3YWwuc3RhdHVzXG4gICAgICAgIH0pKVxuICAgICAgICBzZXRUcmFuc2FjdGlvbnMod2l0aGRyYXdhbFRyYW5zYWN0aW9ucyBhcyBUcmFuc2FjdGlvbltdKVxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFdhbGxldCBkYXRhIGxvYWRlZCB2aWEgZmFsbGJhY2sgbWV0aG9kJywgd2l0aGRyYXdhbFRyYW5zYWN0aW9ucylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB3YWxsZXQgZGF0YTonLCBlcnJvcilcbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxuICAgICAgICB0ZXh0OiAnRmFpbGVkIHRvIGxvYWQgd2FsbGV0IGRhdGEuIFBsZWFzZSB0cnkgYWdhaW4uJyxcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldERhdGFMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGxvYWRCYW5rRGV0YWlscyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYmFua0RhdGEgPSBhd2FpdCBnZXRCYW5rRGV0YWlscyh1c2VyIS51aWQpXG4gICAgICBzZXRCYW5rRGV0YWlscyhiYW5rRGF0YSlcbiAgICAgIGlmIChiYW5rRGF0YSkge1xuICAgICAgICBzZXRCYW5rRm9ybURhdGEoYmFua0RhdGEpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYmFuayBkZXRhaWxzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUJhbmtGb3JtU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuXG4gICAgLy8gUHJldmVudCBtdWx0aXBsZSBzdWJtaXNzaW9uc1xuICAgIGlmIChpc1NhdmluZ0JhbmspIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHNldElzU2F2aW5nQmFuayh0cnVlKVxuICAgICAgYXdhaXQgc2F2ZUJhbmtEZXRhaWxzKHVzZXIhLnVpZCwgYmFua0Zvcm1EYXRhKVxuXG4gICAgICBzZXRCYW5rRGV0YWlscyhiYW5rRm9ybURhdGEpXG4gICAgICBzZXRTaG93QmFua0Zvcm0oZmFsc2UpXG5cbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdzdWNjZXNzJyxcbiAgICAgICAgdGl0bGU6ICdCYW5rIERldGFpbHMgU2F2ZWQnLFxuICAgICAgICB0ZXh0OiAnWW91ciBiYW5rIGRldGFpbHMgaGF2ZSBiZWVuIHNhdmVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICAgIHRpbWVyOiAyMDAwLFxuICAgICAgICBzaG93Q29uZmlybUJ1dHRvbjogZmFsc2VcbiAgICAgIH0pXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGJhbmsgZGV0YWlsczonLCBlcnJvcilcbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxuICAgICAgICB0ZXh0OiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gc2F2ZSBiYW5rIGRldGFpbHMuIFBsZWFzZSB0cnkgYWdhaW4uJyxcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU2F2aW5nQmFuayhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVCYW5rRm9ybUNoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHsgbmFtZSwgdmFsdWUgfSA9IGUudGFyZ2V0XG4gICAgc2V0QmFua0Zvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbbmFtZV06IHZhbHVlXG4gICAgfSkpXG4gIH1cblxuICBjb25zdCBoYW5kbGVXaXRoZHJhdyA9IGFzeW5jICgpID0+IHtcbiAgICAvLyBQcmV2ZW50IG11bHRpcGxlIGNsaWNrcyB3aXRoIHN0cmljdCBjaGVja2luZ1xuICAgIGlmIChpc1dpdGhkcmF3aW5nKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIFdpdGhkcmF3YWwgYWxyZWFkeSBpbiBwcm9ncmVzcywgaWdub3JpbmcgZHVwbGljYXRlIHJlcXVlc3QnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgcGxhbiBpcyBleHBpcmVkIGJhc2VkIG9uIGFjdGl2ZSBkYXlzXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgaXNVc2VyUGxhbkV4cGlyZWQgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvZGF0YVNlcnZpY2UnKVxuICAgICAgY29uc3QgcGxhblN0YXR1cyA9IGF3YWl0IGlzVXNlclBsYW5FeHBpcmVkKHVzZXIhLnVpZClcblxuICAgICAgaWYgKHBsYW5TdGF0dXMuZXhwaXJlZCkge1xuICAgICAgICBTd2FsLmZpcmUoe1xuICAgICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgICAgdGl0bGU6ICdQbGFuIEV4cGlyZWQnLFxuICAgICAgICAgIGh0bWw6IGBcbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8cCBjbGFzcz1cIm1iLTJcIj5Zb3VyIHBsYW4gaGFzIGV4cGlyZWQgYW5kIHlvdSBjYW5ub3QgbWFrZSB3aXRoZHJhd2Fscy48L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTJcIj4ke3BsYW5TdGF0dXMucmVhc29ufTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3M9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDBcIj5QbGVhc2UgdXBncmFkZSB5b3VyIHBsYW4gdG8gY29udGludWUgdXNpbmcgd2l0aGRyYXdhbCBzZXJ2aWNlcy48L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBgLFxuICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAnR28gdG8gUGxhbnMnLFxuICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IHRydWUsXG4gICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ09LJ1xuICAgICAgICB9KS50aGVuKChyZXN1bHQpID0+IHtcbiAgICAgICAgICBpZiAocmVzdWx0LmlzQ29uZmlybWVkKSB7XG4gICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvcGxhbnMnXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgcGxhbiBleHBpcnk6JywgZXJyb3IpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgd2l0aGRyYXdhbHMgYXJlIGJsb2NrZWQgZHVlIHRvIGxlYXZlXG4gICAgaWYgKGlzTGVhdmVCbG9ja2VkKSB7XG4gICAgICBTd2FsLmZpcmUoe1xuICAgICAgICBpY29uOiAnd2FybmluZycsXG4gICAgICAgIHRpdGxlOiAnV2l0aGRyYXdhbCBOb3QgQXZhaWxhYmxlJyxcbiAgICAgICAgdGV4dDogbGVhdmVTdGF0dXMucmVhc29uIHx8ICdXaXRoZHJhd2FscyBhcmUgbm90IGF2YWlsYWJsZSBkdWUgdG8gbGVhdmUuJyxcbiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICdPSydcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHdpdGhkcmF3QW1vdW50KVxuXG4gICAgaWYgKCFhbW91bnQgfHwgYW1vdW50IDw9IDApIHtcbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgIHRpdGxlOiAnSW52YWxpZCBBbW91bnQnLFxuICAgICAgICB0ZXh0OiAnUGxlYXNlIGVudGVyIGEgdmFsaWQgYW1vdW50IHRvIHdpdGhkcmF3JyxcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBpZiAoYW1vdW50IDwgNTApIHtcbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgIHRpdGxlOiAnTWluaW11bSBXaXRoZHJhd2FsJyxcbiAgICAgICAgdGV4dDogJ01pbmltdW0gd2l0aGRyYXdhbCBhbW91bnQgaXMg4oK5NTAnLFxuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChhbW91bnQgPiAod2FsbGV0RGF0YT8ud2FsbGV0IHx8IDApKSB7XG4gICAgICBTd2FsLmZpcmUoe1xuICAgICAgICBpY29uOiAnZXJyb3InLFxuICAgICAgICB0aXRsZTogJ0luc3VmZmljaWVudCBCYWxhbmNlJyxcbiAgICAgICAgdGV4dDogJ1lvdSBkbyBub3QgaGF2ZSBlbm91Z2ggYmFsYW5jZSBpbiB5b3VyIHdhbGxldCcsXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKCFiYW5rRGV0YWlscykge1xuICAgICAgU3dhbC5maXJlKHtcbiAgICAgICAgaWNvbjogJ3dhcm5pbmcnLFxuICAgICAgICB0aXRsZTogJ0JhbmsgRGV0YWlscyBSZXF1aXJlZCcsXG4gICAgICAgIHRleHQ6ICdQbGVhc2UgYWRkIHlvdXIgYmFuayBkZXRhaWxzIGJlZm9yZSBtYWtpbmcgYSB3aXRoZHJhd2FsJyxcbiAgICAgIH0pXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gU2V0IGxvYWRpbmcgc3RhdGUgaW1tZWRpYXRlbHkgdG8gcHJldmVudCByYWNlIGNvbmRpdGlvbnNcbiAgICAgIHNldElzV2l0aGRyYXdpbmcodHJ1ZSlcbiAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFByb2Nlc3Npbmcgd2l0aGRyYXdhbCByZXF1ZXN0IGZvciDigrkke2Ftb3VudH1gKVxuXG4gICAgICAvLyBDcmVhdGUgd2l0aGRyYXdhbCByZXF1ZXN0IGluIEZpcmVzdG9yZSB3aXRoIGF0b21pYyBvcGVyYXRpb25zXG4gICAgICBhd2FpdCBjcmVhdGVXaXRoZHJhd2FsUmVxdWVzdCh1c2VyIS51aWQsIGFtb3VudCwgYmFua0RldGFpbHMpXG5cbiAgICAgIC8vIFJlbG9hZCB3YWxsZXQgZGF0YSB0byBzaG93IHVwZGF0ZWQgdHJhbnNhY3Rpb25zXG4gICAgICBhd2FpdCBsb2FkV2FsbGV0RGF0YSgpXG5cbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdzdWNjZXNzJyxcbiAgICAgICAgdGl0bGU6ICdXaXRoZHJhd2FsIFJlcXVlc3QgU3VibWl0dGVkJyxcbiAgICAgICAgdGV4dDogYFlvdXIgd2l0aGRyYXdhbCByZXF1ZXN0IGZvciDigrkke2Ftb3VudH0gaGFzIGJlZW4gc3VibWl0dGVkIGFuZCB3aWxsIGJlIHByb2Nlc3NlZCB3aXRoaW4gMjQtNDggaG91cnMuYCxcbiAgICAgIH0pXG5cbiAgICAgIHNldFdpdGhkcmF3QW1vdW50KCcnKVxuICAgICAgY29uc29sZS5sb2coYOKchSBXaXRoZHJhd2FsIHJlcXVlc3QgY29tcGxldGVkIHN1Y2Nlc3NmdWxseWApXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHByb2Nlc3Npbmcgd2l0aGRyYXdhbDonLCBlcnJvcilcbiAgICAgIFN3YWwuZmlyZSh7XG4gICAgICAgIGljb246ICdlcnJvcicsXG4gICAgICAgIHRpdGxlOiAnV2l0aGRyYXdhbCBGYWlsZWQnLFxuICAgICAgICB0ZXh0OiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gcHJvY2VzcyB3aXRoZHJhd2FsIHJlcXVlc3QuIFBsZWFzZSB0cnkgYWdhaW4uJyxcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzV2l0aGRyYXdpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoYW1vdW50OiBudW1iZXIgfCB1bmRlZmluZWQpID0+IHtcbiAgICBpZiAoYW1vdW50ID09PSB1bmRlZmluZWQgfHwgYW1vdW50ID09PSBudWxsIHx8IGlzTmFOKGFtb3VudCkpIHtcbiAgICAgIHJldHVybiAn4oK5MC4wMCdcbiAgICB9XG4gICAgcmV0dXJuIGDigrkke2Ftb3VudC50b0ZpeGVkKDIpfWBcbiAgfVxuXG5cblxuICBpZiAobG9hZGluZyB8fCBkYXRhTG9hZGluZyB8fCBpc0NoZWNraW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgIHtsb2FkaW5nID8gJ0xvYWRpbmcuLi4nIDogaXNDaGVja2luZyA/ICdDaGVja2luZyBub3RpZmljYXRpb25zLi4uJyA6ICdMb2FkaW5nIHdhbGxldC4uLid9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIFNob3cgYmxvY2tpbmcgbm90aWZpY2F0aW9ucyBpZiBhbnkgZXhpc3RcbiAgaWYgKGhhc0Jsb2NraW5nTm90aWZpY2F0aW9ucyAmJiB1c2VyKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCbG9ja2luZ05vdGlmaWNhdGlvbk1vZGFsXG4gICAgICAgIHVzZXJJZD17dXNlci51aWR9XG4gICAgICAgIG9uQWxsUmVhZD17bWFya0FsbEFzUmVhZH1cbiAgICAgIC8+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBwLTRcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC00IG1iLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiIGNsYXNzTmFtZT1cImdsYXNzLWJ1dHRvbiBweC00IHB5LTIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLWFycm93LWxlZnQgbXItMlwiPjwvaT5cbiAgICAgICAgICAgIEJhY2sgdG8gRGFzaGJvYXJkXG4gICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+TXkgV2FsbGV0PC9oMT5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtsb2FkV2FsbGV0RGF0YX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdsYXNzLWJ1dHRvbiBweC00IHB5LTIgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLXN5bmMtYWx0IG1yLTJcIj48L2k+XG4gICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIHsvKiBTaW5nbGUgV2FsbGV0IEJhbGFuY2UgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC02IG1iLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5NeSBXYWxsZXQ8L2gzPlxuICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS13YWxsZXQgdGV4dC1ncmVlbi00MDAgdGV4dC0zeGxcIj48L2k+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmVlbi00MDAgbWItMlwiPlxuICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeSh3YWxsZXREYXRhPy53YWxsZXQgfHwgMCl9XG4gICAgICAgIDwvcD5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MFwiPlRvdGFsIGF2YWlsYWJsZSBiYWxhbmNlPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBCYW5rIERldGFpbHMgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtY2FyZCBwLTYgbWItNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS11bml2ZXJzaXR5IG1yLTJcIj48L2k+XG4gICAgICAgICAgICBCYW5rIERldGFpbHNcbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIHtiYW5rRGV0YWlscyAmJiAhc2hvd0JhbmtGb3JtICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0JhbmtGb3JtKHRydWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJnbGFzcy1idXR0b24gcHgtNCBweS0yIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtZWRpdCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICBFZGl0XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7IWJhbmtEZXRhaWxzICYmICFzaG93QmFua0Zvcm0gPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS02XCI+XG4gICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtdW5pdmVyc2l0eSB0ZXh0LXdoaXRlLzMwIHRleHQtNHhsIG1iLTRcIj48L2k+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIG1iLTRcIj5ObyBiYW5rIGRldGFpbHMgYWRkZWQgeWV0PC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QmFua0Zvcm0odHJ1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLXBsdXMgbXItMlwiPjwvaT5cbiAgICAgICAgICAgICAgQWRkIEJhbmsgRGV0YWlsc1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiBzaG93QmFua0Zvcm0gPyAoXG4gICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZUJhbmtGb3JtU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIEFjY291bnQgSG9sZGVyIE5hbWUgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiYWNjb3VudEhvbGRlck5hbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2JhbmtGb3JtRGF0YS5hY2NvdW50SG9sZGVyTmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVCYW5rRm9ybUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBhY2NvdW50IGhvbGRlciBuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgQmFuayBOYW1lICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJhbmtOYW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtiYW5rRm9ybURhdGEuYmFua05hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQmFua0Zvcm1DaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYmFuayBuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgQWNjb3VudCBOdW1iZXIgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiYWNjb3VudE51bWJlclwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17YmFua0Zvcm1EYXRhLmFjY291bnROdW1iZXJ9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQmFua0Zvcm1DaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYWNjb3VudCBudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBJRlNDIENvZGUgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiaWZzY0NvZGVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2JhbmtGb3JtRGF0YS5pZnNjQ29kZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVCYW5rRm9ybUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBJRlNDIGNvZGUgKGUuZy4sIFNCSU4wMDAxMjM0KVwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NhdmluZ0Jhbmt9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtpc1NhdmluZ0JhbmsgPyAnYnRuLWRpc2FibGVkIGN1cnNvci1ub3QtYWxsb3dlZCBvcGFjaXR5LTUwJyA6ICdidG4tcHJpbWFyeSBob3ZlcjpiZy1ibHVlLTYwMCd9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1NhdmluZ0JhbmsgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgbXItMiB3LTUgaC01XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFNhdmluZyBCYW5rIERldGFpbHMuLi5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtc2F2ZSBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgICAgICBTYXZlIEJhbmsgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QmFua0Zvcm0oZmFsc2UpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1zZWNvbmRhcnlcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9mb3JtPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvMTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbVwiPkFjY291bnQgSG9sZGVyPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57YmFua0RldGFpbHM/LmFjY291bnRIb2xkZXJOYW1lfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MCB0ZXh0LXNtXCI+QmFuayBOYW1lPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57YmFua0RldGFpbHM/LmJhbmtOYW1lfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MCB0ZXh0LXNtXCI+QWNjb3VudCBOdW1iZXI8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgKioqKntiYW5rRGV0YWlscz8uYWNjb3VudE51bWJlci5zbGljZSgtNCl9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc21cIj5JRlNDIENvZGU8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPntiYW5rRGV0YWlscz8uaWZzY0NvZGV9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBXaXRoZHJhd2FsIFNlY3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC02IG1iLTZcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1tb25leS1iaWxsLXdhdmUgbXItMlwiPjwvaT5cbiAgICAgICAgICBXaXRoZHJhdyBGdW5kc1xuICAgICAgICA8L2gzPlxuXG4gICAgICAgIHsvKiBUcmlhbCBQbGFuIFdhcm5pbmcgKi99XG4gICAgICAgIHt1c2VyRGF0YT8ucGxhbiA9PT0gJ1RyaWFsJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtNCBiZy1yZWQtNTAwLzIwIGJvcmRlciBib3JkZXItcmVkLTUwMC8zMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTJcIj5cbiAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLWxvY2sgdGV4dC1yZWQtNDAwIG1yLTJcIj48L2k+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCBmb250LW1lZGl1bVwiPlRyaWFsIFBsYW4gUmVzdHJpY3Rpb248L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvODAgdGV4dC1zbSBtYi0zXCI+XG4gICAgICAgICAgICAgIFRyaWFsIHBsYW4gdXNlcnMgY2Fubm90IG1ha2Ugd2l0aGRyYXdhbHMuIFBsZWFzZSB1cGdyYWRlIHRvIGEgcGFpZCBwbGFuIHRvIGVuYWJsZSB3aXRoZHJhd2FsIGZ1bmN0aW9uYWxpdHkuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wbGFuc1wiIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGlubGluZS1ibG9ja1wiPlxuICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1hcnJvdy11cCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgIFVwZ3JhZGUgUGxhblxuICAgICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBXaXRoZHJhd2FsIFRpbWluZyBJbmZvICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgcC00IGJnLWJsdWUtNTAwLzIwIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItMlwiPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLWNsb2NrIHRleHQtYmx1ZS00MDAgbXItMlwiPjwvaT5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgZm9udC1tZWRpdW1cIj5XaXRoZHJhd2FsIFRpbWluZ3M8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MCB0ZXh0LXNtIG1iLTJcIj5cbiAgICAgICAgICAgIFdpdGhkcmF3YWxzIGFyZSBvbmx5IGFsbG93ZWQgYmV0d2VlbiA8c3Ryb25nPjEwOjAwIEFNIHRvIDY6MDAgUE08L3N0cm9uZz4gb24gbm9uLWxlYXZlIGRheXMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgQ3VycmVudCB0aW1lOiB7bmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKX0gfFxuICAgICAgICAgICAgICBTdGF0dXM6IHt3aXRoZHJhd2FsQWxsb3dlZC5hbGxvd2VkID8gKFxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIGZvbnQtbWVkaXVtXCI+4pyTIEF2YWlsYWJsZTwvc3Bhbj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgZm9udC1tZWRpdW1cIj7inJcgTm90IEF2YWlsYWJsZTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17Y2hlY2tXaXRoZHJhd2FsRWxpZ2liaWxpdHl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtjaGVja2luZ1dpdGhkcmF3YWx9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTMwMCB0ZXh0LXhzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NoZWNraW5nV2l0aGRyYXdhbCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgdy0zIGgtM1wiPjwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1zeW5jLWFsdFwiPjwvaT5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHshd2l0aGRyYXdhbEFsbG93ZWQuYWxsb3dlZCAmJiB3aXRoZHJhd2FsQWxsb3dlZC5yZWFzb24gJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIHRleHQtc20gbXQtMlwiPlxuICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtZXhjbGFtYXRpb24tdHJpYW5nbGUgbXItMVwiPjwvaT5cbiAgICAgICAgICAgICAge3dpdGhkcmF3YWxBbGxvd2VkLnJlYXNvbn1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7IWJhbmtEZXRhaWxzID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktNlwiPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLWV4Y2xhbWF0aW9uLXRyaWFuZ2xlIHRleHQteWVsbG93LTQwMCB0ZXh0LTN4bCBtYi00XCI+PC9pPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MCBtYi00XCI+UGxlYXNlIGFkZCB5b3VyIGJhbmsgZGV0YWlscyBiZWZvcmUgbWFraW5nIGEgd2l0aGRyYXdhbDwvcD5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0JhbmtGb3JtKHRydWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS11bml2ZXJzaXR5IG1yLTJcIj48L2k+XG4gICAgICAgICAgICAgIEFkZCBCYW5rIERldGFpbHNcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3dpdGhkcmF3QW1vdW50fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0V2l0aGRyYXdBbW91bnQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYW1vdW50IHRvIHdpdGhkcmF3IChNaW46IOKCuTUwKVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dCBmbGV4LTFcIlxuICAgICAgICAgICAgICAgIG1pbj1cIjUwXCJcbiAgICAgICAgICAgICAgICBtYXg9e3dhbGxldERhdGE/LndhbGxldCB8fCAwfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlV2l0aGRyYXd9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzV2l0aGRyYXdpbmcgfHwgIXdpdGhkcmF3QW1vdW50IHx8ICF3aXRoZHJhd2FsQWxsb3dlZC5hbGxvd2VkIHx8IHBhcnNlRmxvYXQod2l0aGRyYXdBbW91bnQpIDw9IDB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgIGlzV2l0aGRyYXdpbmdcbiAgICAgICAgICAgICAgICAgICAgPyAnYnRuLWRpc2FibGVkIGN1cnNvci1ub3QtYWxsb3dlZCBvcGFjaXR5LTUwIHBvaW50ZXItZXZlbnRzLW5vbmUnXG4gICAgICAgICAgICAgICAgICAgIDogd2l0aGRyYXdhbEFsbG93ZWQuYWxsb3dlZCAmJiB3aXRoZHJhd0Ftb3VudCAmJiBwYXJzZUZsb2F0KHdpdGhkcmF3QW1vdW50KSA+IDBcbiAgICAgICAgICAgICAgICAgICAgICA/ICdidG4tcHJpbWFyeSBob3ZlcjpiZy1ibHVlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdidG4tZGlzYWJsZWQgY3Vyc29yLW5vdC1hbGxvd2VkIG9wYWNpdHktNTAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgcG9pbnRlckV2ZW50czogaXNXaXRoZHJhd2luZyA/ICdub25lJyA6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzV2l0aGRyYXdpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgbXItMiB3LTUgaC01XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFByb2Nlc3NpbmcgV2l0aGRyYXdhbC4uLlxuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1kb3dubG9hZCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgICAgICBXaXRoZHJhdyDigrl7d2l0aGRyYXdBbW91bnQgfHwgJzAnfVxuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbSBtdC0yXCI+XG4gICAgICAgICAgICAgIEF2YWlsYWJsZToge2Zvcm1hdEN1cnJlbmN5KHdhbGxldERhdGE/LndhbGxldCB8fCAwKX0gfCBNaW5pbXVtOiDigrk1MFxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBXaXRoZHJhd2FsIEhpc3RvcnkgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLW1vbmV5LWJpbGwtd2F2ZSBtci0yXCI+PC9pPlxuICAgICAgICAgICAgV2l0aGRyYXdhbCBIaXN0b3J5XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtsb2FkV2FsbGV0RGF0YX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdsYXNzLWJ1dHRvbiBweC00IHB5LTIgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLXN5bmMtYWx0IG1yLTJcIj48L2k+XG4gICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHt0cmFuc2FjdGlvbnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLW1vbmV5LWJpbGwtd2F2ZSB0ZXh0LXdoaXRlLzMwIHRleHQtNHhsIG1iLTRcIj48L2k+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIG1iLTJcIj5ObyB3aXRoZHJhd2FsIHJlcXVlc3RzIHlldDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNDAgdGV4dC1zbVwiPllvdXIgd2l0aGRyYXdhbCByZXF1ZXN0cyB3aWxsIGFwcGVhciBoZXJlPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICB7dHJhbnNhY3Rpb25zLm1hcCgodHJhbnNhY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGtleT17dHJhbnNhY3Rpb24uaWR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBiZy13aGl0ZS8xMCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1tb25leS1iaWxsLXdhdmUgdGV4dC1yZWQtNDAwIG1yLTNcIj48L2k+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e3RyYW5zYWN0aW9uLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3RyYW5zYWN0aW9uLmRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCl9IGF0IHt0cmFuc2FjdGlvbi5kYXRlLnRvTG9jYWxlVGltZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXJlZC00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KE1hdGguYWJzKHRyYW5zYWN0aW9uLmFtb3VudCkpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zYWN0aW9uLnN0YXR1cyA9PT0gJ3BlbmRpbmcnID8gJ2JnLXllbGxvdy01MDAvMjAgdGV4dC15ZWxsb3ctNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zYWN0aW9uLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJyA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNhY3Rpb24uc3RhdHVzID09PSAncmVqZWN0ZWQnID8gJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zYWN0aW9uLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAnYmctYmx1ZS01MDAvMjAgdGV4dC1ibHVlLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAnYmctZ3JheS01MDAvMjAgdGV4dC1ncmF5LTQwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge3RyYW5zYWN0aW9uLnN0YXR1cyA9PT0gJ3BlbmRpbmcnID8gJ+KPsyBQZW5kaW5nJyA6XG4gICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbi5zdGF0dXMgPT09ICdhcHByb3ZlZCcgPyAn4pyFIEFwcHJvdmVkJyA6XG4gICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbi5zdGF0dXMgPT09ICdyZWplY3RlZCcgPyAn4p2MIFJlamVjdGVkJyA6XG4gICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbi5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ+KchSBDb21wbGV0ZWQnIDpcbiAgICAgICAgICAgICAgICAgICAgIHRyYW5zYWN0aW9uLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsInVzZVJlcXVpcmVBdXRoIiwidXNlQmxvY2tpbmdOb3RpZmljYXRpb25zIiwidXNlTGVhdmVNb25pdG9yIiwiZ2V0V2FsbGV0RGF0YSIsImdldEJhbmtEZXRhaWxzIiwic2F2ZUJhbmtEZXRhaWxzIiwiY3JlYXRlV2l0aGRyYXdhbFJlcXVlc3QiLCJnZXRVc2VyV2l0aGRyYXdhbHMiLCJjaGVja1dpdGhkcmF3YWxBbGxvd2VkIiwiZ2V0VXNlckRhdGEiLCJvcHRpbWl6ZWRTZXJ2aWNlIiwiQmxvY2tpbmdOb3RpZmljYXRpb25Nb2RhbCIsIlN3YWwiLCJXYWxsZXRQYWdlIiwidXNlciIsImxvYWRpbmciLCJoYXNCbG9ja2luZ05vdGlmaWNhdGlvbnMiLCJpc0NoZWNraW5nIiwibWFya0FsbEFzUmVhZCIsInVpZCIsImlzQmxvY2tlZCIsImlzTGVhdmVCbG9ja2VkIiwibGVhdmVTdGF0dXMiLCJ1c2VySWQiLCJjaGVja0ludGVydmFsIiwiZW5hYmxlZCIsIndhbGxldERhdGEiLCJzZXRXYWxsZXREYXRhIiwidHJhbnNhY3Rpb25zIiwic2V0VHJhbnNhY3Rpb25zIiwiZGF0YUxvYWRpbmciLCJzZXREYXRhTG9hZGluZyIsIndpdGhkcmF3QW1vdW50Iiwic2V0V2l0aGRyYXdBbW91bnQiLCJpc1dpdGhkcmF3aW5nIiwic2V0SXNXaXRoZHJhd2luZyIsImJhbmtEZXRhaWxzIiwic2V0QmFua0RldGFpbHMiLCJzaG93QmFua0Zvcm0iLCJzZXRTaG93QmFua0Zvcm0iLCJiYW5rRm9ybURhdGEiLCJzZXRCYW5rRm9ybURhdGEiLCJhY2NvdW50SG9sZGVyTmFtZSIsImFjY291bnROdW1iZXIiLCJpZnNjQ29kZSIsImJhbmtOYW1lIiwiaXNTYXZpbmdCYW5rIiwic2V0SXNTYXZpbmdCYW5rIiwid2l0aGRyYXdhbEFsbG93ZWQiLCJzZXRXaXRoZHJhd2FsQWxsb3dlZCIsImFsbG93ZWQiLCJjaGVja2luZ1dpdGhkcmF3YWwiLCJzZXRDaGVja2luZ1dpdGhkcmF3YWwiLCJ1c2VyRGF0YSIsInNldFVzZXJEYXRhIiwidHJpZ2dlckRhaWx5UHJvY2VzcyIsImNoZWNrQW5kUnVuRGFpbHlQcm9jZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwibG9hZFdhbGxldERhdGEiLCJsb2FkQmFua0RldGFpbHMiLCJsb2FkVXNlckRhdGEiLCJjaGVja1dpdGhkcmF3YWxFbGlnaWJpbGl0eSIsInJlYXNvbiIsImRhdGEiLCJyZXN1bHQiLCJFcnJvciIsImxvZyIsIndhbGxldFJlc3VsdCIsInRyYW5zYWN0aW9uc1Jlc3VsdCIsIlByb21pc2UiLCJhbGwiLCJnZXRVc2VyVHJhbnNhY3Rpb25zIiwiZm9ybWF0dGVkVHJhbnNhY3Rpb25zIiwibWFwIiwidHJhbnNhY3Rpb24iLCJpZCIsInR5cGUiLCJhbW91bnQiLCJNYXRoIiwiYWJzIiwiZGVzY3JpcHRpb24iLCJkYXRlIiwic3RhdHVzIiwib3B0aW1pemVkRXJyb3IiLCJ3YXJuIiwid2l0aGRyYXdhbHNSZXN1bHQiLCJ3aXRoZHJhd2FsVHJhbnNhY3Rpb25zIiwid2l0aGRyYXdhbCIsImZpcmUiLCJpY29uIiwidGl0bGUiLCJ0ZXh0IiwiYmFua0RhdGEiLCJoYW5kbGVCYW5rRm9ybVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInRpbWVyIiwic2hvd0NvbmZpcm1CdXR0b24iLCJtZXNzYWdlIiwiaGFuZGxlQmFua0Zvcm1DaGFuZ2UiLCJuYW1lIiwidmFsdWUiLCJ0YXJnZXQiLCJwcmV2IiwiaGFuZGxlV2l0aGRyYXciLCJpc1VzZXJQbGFuRXhwaXJlZCIsInBsYW5TdGF0dXMiLCJleHBpcmVkIiwiaHRtbCIsImNvbmZpcm1CdXR0b25UZXh0Iiwic2hvd0NhbmNlbEJ1dHRvbiIsImNhbmNlbEJ1dHRvblRleHQiLCJ0aGVuIiwiaXNDb25maXJtZWQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJwYXJzZUZsb2F0Iiwid2FsbGV0IiwiZm9ybWF0Q3VycmVuY3kiLCJ1bmRlZmluZWQiLCJpc05hTiIsInRvRml4ZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwib25BbGxSZWFkIiwiaGVhZGVyIiwiaSIsImgxIiwiYnV0dG9uIiwib25DbGljayIsImgzIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsImRpc2FibGVkIiwic2xpY2UiLCJwbGFuIiwic3BhbiIsInN0cm9uZyIsIkRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJtaW4iLCJtYXgiLCJzdHlsZSIsInBvaW50ZXJFdmVudHMiLCJsZW5ndGgiLCJ0b0xvY2FsZURhdGVTdHJpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wallet/page.tsx\n"));

/***/ })

});