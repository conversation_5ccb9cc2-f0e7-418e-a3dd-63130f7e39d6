"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_adminDataService_ts";
exports.ids = ["_ssr_src_lib_adminDataService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/adminDataService.ts":
/*!*************************************!*\
  !*** ./src/lib/adminDataService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAdminCache: () => (/* binding */ clearAdminCache),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getAdminDashboardStats: () => (/* binding */ getAdminDashboardStats),\n/* harmony export */   getAllPendingWithdrawals: () => (/* binding */ getAllPendingWithdrawals),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllWithdrawals: () => (/* binding */ getAllWithdrawals),\n/* harmony export */   getTotalUserCount: () => (/* binding */ getTotalUserCount),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   getWithdrawals: () => (/* binding */ getWithdrawals),\n/* harmony export */   searchUsers: () => (/* binding */ searchUsers),\n/* harmony export */   searchUsersByMobile: () => (/* binding */ searchUsersByMobile),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   updateWithdrawalStatus: () => (/* binding */ updateWithdrawalStatus)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _dataService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\");\n\n\n\n// Cache for admin data\nconst adminCache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\n// Helper function to check cache\nfunction getCachedData(key) {\n    const cached = adminCache.get(key);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.data;\n    }\n    return null;\n}\n// Helper function to set cache\nfunction setCachedData(key, data) {\n    adminCache.set(key, {\n        data,\n        timestamp: Date.now()\n    });\n}\n// Get dashboard statistics\nasync function getAdminDashboardStats() {\n    const cacheKey = 'dashboard-stats';\n    const cached = getCachedData(cacheKey);\n    if (cached) return cached;\n    try {\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayTimestamp = firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today);\n        // Get total users\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users));\n        const totalUsers = usersSnapshot.size;\n        // Get today's new users\n        const todayUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, '>=', todayTimestamp));\n        const todayUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(todayUsersQuery);\n        const todayUsers = todayUsersSnapshot.size;\n        // Calculate total videos and earnings from all users\n        let totalVideos = 0;\n        let totalEarnings = 0;\n        let todayVideos = 0;\n        let todayEarnings = 0;\n        usersSnapshot.forEach((doc)=>{\n            const data = doc.data();\n            totalVideos += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.totalVideos] || 0;\n            totalEarnings += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.wallet] || 0;\n            // Check if last video was today\n            const lastVideoDate = data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.lastVideoDate]?.toDate();\n            if (lastVideoDate && lastVideoDate.toDateString() === today.toDateString()) {\n                todayVideos += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.todayVideos] || 0;\n            }\n        });\n        // Get today's transactions for earnings - simplified to avoid index requirement\n        try {\n            const allTransactionsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.type, '==', 'video_earning'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1000));\n            const allTransactionsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allTransactionsQuery);\n            // Filter in memory for today's transactions\n            allTransactionsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const transactionDate = data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date]?.toDate();\n                if (transactionDate && transactionDate >= today) {\n                    todayEarnings += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.amount] || 0;\n                }\n            });\n        } catch (transactionError) {\n            console.warn('Could not fetch today\\'s transactions:', transactionError);\n        // Continue without today's earnings data\n        }\n        // Get pending withdrawals\n        const pendingWithdrawalsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'));\n        const pendingWithdrawalsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(pendingWithdrawalsQuery);\n        const pendingWithdrawals = pendingWithdrawalsSnapshot.size;\n        // Get today's withdrawals\n        const todayWithdrawalsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('date', '>=', todayTimestamp));\n        const todayWithdrawalsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(todayWithdrawalsQuery);\n        const todayWithdrawalsCount = todayWithdrawalsSnapshot.size;\n        const stats = {\n            totalUsers,\n            totalVideos,\n            totalEarnings,\n            pendingWithdrawals,\n            todayUsers,\n            todayVideos,\n            todayEarnings,\n            todayWithdrawals: todayWithdrawalsCount\n        };\n        setCachedData(cacheKey, stats);\n        return stats;\n    } catch (error) {\n        console.error('Error getting admin dashboard stats:', error);\n        throw error;\n    }\n}\n// Get all users with pagination\nasync function getUsers(pageSize = 50, lastDoc = null) {\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const users = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]?.toDate(),\n                planExpiry: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]?.toDate()\n            }));\n        return {\n            users,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting users:', error);\n        throw error;\n    }\n}\n// Search users by mobile number (exact match)\nasync function searchUsersByMobile(mobile) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.mobile, '==', mobile));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]?.toDate(),\n                planExpiry: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error searching users by mobile:', error);\n        throw error;\n    }\n}\n// Enhanced search function for users (searches across all users)\nasync function searchUsers(searchTerm) {\n    try {\n        if (!searchTerm || searchTerm.trim().length === 0) {\n            return [];\n        }\n        const searchTermLower = searchTerm.toLowerCase().trim();\n        // Get all users (we need to search across all users, not just paginated results)\n        // Note: For large datasets, consider implementing server-side search or using Algolia\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const allUsers = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]?.toDate(),\n                planExpiry: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]?.toDate()\n            }));\n        // Filter users based on search term (client-side filtering)\n        const filteredUsers = allUsers.filter((user)=>{\n            // Safely convert to string and then to lowercase\n            const name = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.name] || '').toLowerCase();\n            const email = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.email] || '').toLowerCase();\n            const mobile = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.mobile] || '').toLowerCase();\n            const referralCode = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.referralCode] || '').toLowerCase();\n            return name.includes(searchTermLower) || email.includes(searchTermLower) || mobile.includes(searchTermLower) || referralCode.includes(searchTermLower);\n        });\n        return filteredUsers;\n    } catch (error) {\n        console.error('Error searching users:', error);\n        throw error;\n    }\n}\n// Get all users without pagination (for search and export)\nasync function getAllUsers() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]?.toDate(),\n                planExpiry: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting all users:', error);\n        throw error;\n    }\n}\n// Get total user count\nasync function getTotalUserCount() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.size;\n    } catch (error) {\n        console.error('Error getting total user count:', error);\n        throw error;\n    }\n}\n// Get all transactions with pagination\nasync function getTransactions(pageSize = 50, lastDoc = null) {\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date]?.toDate()\n            }));\n        return {\n            transactions,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        throw error;\n    }\n}\n// Get all withdrawals with pagination\nasync function getWithdrawals(pageSize = 50, lastDoc = null) {\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n        return {\n            withdrawals,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting withdrawals:', error);\n        throw error;\n    }\n}\n// Get all pending withdrawals without pagination (for admin full view)\nasync function getAllPendingWithdrawals() {\n    try {\n        console.log('🔍 Loading ALL pending withdrawals...');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n        console.log(`✅ Loaded ${withdrawals.length} pending withdrawals`);\n        return withdrawals;\n    } catch (error) {\n        console.error('Error getting all pending withdrawals:', error);\n        throw error;\n    }\n}\n// Get all withdrawals without pagination (for admin full view)\nasync function getAllWithdrawals() {\n    try {\n        console.log('🔍 Loading ALL withdrawals...');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n        console.log(`✅ Loaded ${withdrawals.length} total withdrawals`);\n        return withdrawals;\n    } catch (error) {\n        console.error('Error getting all withdrawals:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUser(userId, updateData) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users, userId), updateData);\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error updating user:', error);\n        throw error;\n    }\n}\n// Delete user\nasync function deleteUser(userId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users, userId));\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error deleting user:', error);\n        throw error;\n    }\n}\n// Update withdrawal status\nasync function updateWithdrawalStatus(withdrawalId, status, adminNotes) {\n    try {\n        // Get withdrawal details first\n        const withdrawalDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals, withdrawalId));\n        if (!withdrawalDoc.exists()) {\n            throw new Error('Withdrawal not found');\n        }\n        const withdrawalData = withdrawalDoc.data();\n        const { userId, amount, status: currentStatus } = withdrawalData;\n        const updateData = {\n            status,\n            updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        if (adminNotes) {\n            updateData.adminNotes = adminNotes;\n        }\n        // Update withdrawal status\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals, withdrawalId), updateData);\n        // If withdrawal is being approved, just add a transaction record (amount already debited)\n        if (status === 'approved' && currentStatus !== 'approved') {\n            const { addTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\"));\n            // Add transaction record for withdrawal approval\n            await addTransaction(userId, {\n                type: 'withdrawal_approved',\n                amount: 0,\n                description: `Withdrawal approved - ₹${amount} processed for transfer`\n            });\n        }\n        // If withdrawal is being rejected, credit back the amount (since it was already debited)\n        if (status === 'rejected' && currentStatus !== 'rejected') {\n            const { updateWalletBalance, addTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\"));\n            // Credit back the amount to user's wallet\n            await updateWalletBalance(userId, amount);\n            // Add transaction record for withdrawal credit back\n            await addTransaction(userId, {\n                type: 'withdrawal_rejected',\n                amount: amount,\n                description: `Withdrawal rejected - ₹${amount} credited back to wallet`\n            });\n        }\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error updating withdrawal status:', error);\n        throw error;\n    }\n}\n// Clear admin cache\nfunction clearAdminCache() {\n    adminCache.clear();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/adminDataService.ts\n");

/***/ })

};
;