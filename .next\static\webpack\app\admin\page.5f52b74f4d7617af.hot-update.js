"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/optimizedDataService.ts":
/*!*****************************************!*\
  !*** ./src/lib/optimizedDataService.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areFunctionsAvailable: () => (/* binding */ areFunctionsAvailable),\n/* harmony export */   createOptimizedAdminNotification: () => (/* binding */ createOptimizedAdminNotification),\n/* harmony export */   getHybridAdminDashboardStats: () => (/* binding */ getHybridAdminDashboardStats),\n/* harmony export */   getHybridDashboardData: () => (/* binding */ getHybridDashboardData),\n/* harmony export */   getOptimizedAdminDashboardStats: () => (/* binding */ getOptimizedAdminDashboardStats),\n/* harmony export */   getOptimizedAdminNotifications: () => (/* binding */ getOptimizedAdminNotifications),\n/* harmony export */   getOptimizedAdminUsers: () => (/* binding */ getOptimizedAdminUsers),\n/* harmony export */   getOptimizedAdminWithdrawals: () => (/* binding */ getOptimizedAdminWithdrawals),\n/* harmony export */   getOptimizedDashboardData: () => (/* binding */ getOptimizedDashboardData),\n/* harmony export */   getOptimizedUserNotifications: () => (/* binding */ getOptimizedUserNotifications),\n/* harmony export */   getOptimizedUserTransactions: () => (/* binding */ getOptimizedUserTransactions),\n/* harmony export */   optimizedService: () => (/* binding */ optimizedService),\n/* harmony export */   processOptimizedWithdrawal: () => (/* binding */ processOptimizedWithdrawal),\n/* harmony export */   submitOptimizedVideoBatch: () => (/* binding */ submitOptimizedVideoBatch)\n/* harmony export */ });\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/functions */ \"(app-pages-browser)/./node_modules/firebase/functions/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n// 🎯 OPTIMIZED DATA SERVICE\n// Uses Firebase Functions to reduce Firestore reads by 60-75%\n// Initialize Firebase Functions\nconst getUserDashboardDataFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserDashboardData');\nconst submitVideoBatchFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'submitVideoBatch');\nconst processWithdrawalRequestFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'processWithdrawalRequest');\nconst getUserNotificationsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserNotifications');\nconst getUserTransactionsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserTransactions');\nconst getAdminWithdrawalsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getAdminWithdrawals');\nconst getAdminDashboardStatsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getAdminDashboardStats');\nconst getAdminUsersFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getAdminUsers');\nconst getAdminNotificationsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getAdminNotifications');\nconst createAdminNotificationFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'createAdminNotification');\n// Dashboard Data - Replaces multiple client-side reads\nasync function getOptimizedDashboardData(userId) {\n    try {\n        console.log('🚀 Using optimized dashboard data function...');\n        const result = await getUserDashboardDataFunction({\n            userId\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Dashboard data loaded via optimized function');\n                // Map the Firebase Function response to the expected frontend format\n                const functionData = data.data;\n                return {\n                    userData: {\n                        name: functionData.user.name,\n                        email: functionData.user.email,\n                        mobile: functionData.user.mobile,\n                        referralCode: functionData.user.referralCode,\n                        plan: functionData.user.plan,\n                        planExpiry: null,\n                        activeDays: functionData.user.activeDays\n                    },\n                    walletData: {\n                        wallet: functionData.user.wallet\n                    },\n                    videoData: {\n                        totalVideos: functionData.videos.total,\n                        todayVideos: functionData.videos.today,\n                        remainingVideos: functionData.videos.remaining\n                    }\n                };\n            }\n        }\n        throw new Error('Invalid response from dashboard function');\n    } catch (error) {\n        console.error('❌ Error in optimized dashboard data:', error);\n        throw error;\n    }\n}\n// Video Batch Submission - Atomic operation\nasync function submitOptimizedVideoBatch(userId) {\n    let videoCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n    try {\n        console.log('🚀 Using optimized video batch submission...');\n        const result = await submitVideoBatchFunction({\n            userId,\n            videoCount\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Video batch submitted via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from video batch function');\n    } catch (error) {\n        console.error('❌ Error in optimized video batch submission:', error);\n        throw error;\n    }\n}\n// Withdrawal Request - Atomic operation with validation\nasync function processOptimizedWithdrawal(withdrawalData) {\n    try {\n        console.log('🚀 Using optimized withdrawal processing...');\n        const result = await processWithdrawalRequestFunction(withdrawalData);\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Withdrawal processed via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from withdrawal function');\n    } catch (error) {\n        console.error('❌ Error in optimized withdrawal processing:', error);\n        throw error;\n    }\n}\n// User Notifications - Server-side filtering\nasync function getOptimizedUserNotifications(userId) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        console.log('🚀 Using optimized notifications function...');\n        const result = await getUserNotificationsFunction({\n            userId,\n            limit\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Notifications loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from notifications function');\n    } catch (error) {\n        console.error('❌ Error in optimized notifications:', error);\n        throw error;\n    }\n}\n// User Transactions - Optimized with pagination\nasync function getOptimizedUserTransactions(userId) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, type = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'all';\n    try {\n        console.log('🚀 Using optimized transactions function...');\n        const result = await getUserTransactionsFunction({\n            userId,\n            limit,\n            type\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Transactions loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from transactions function');\n    } catch (error) {\n        console.error('❌ Error in optimized transactions:', error);\n        throw error;\n    }\n}\n// Admin Withdrawals - Batch processing with user data\nasync function getOptimizedAdminWithdrawals() {\n    let showAllWithdrawals = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n    try {\n        console.log('🚀 Using optimized admin withdrawals function...');\n        const result = await getAdminWithdrawalsFunction({\n            showAllWithdrawals\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Admin withdrawals loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from admin withdrawals function');\n    } catch (error) {\n        console.error('❌ Error in optimized admin withdrawals:', error);\n        throw error;\n    }\n}\n// Fallback detection - Check if functions are available\nasync function areFunctionsAvailable() {\n    try {\n        // Try a simple function call to test connectivity\n        await getUserDashboardDataFunction({\n            userId: 'test'\n        });\n        return true;\n    } catch (error) {\n        console.warn('⚠️ Firebase Functions not available, falling back to direct Firestore');\n        return false;\n    }\n}\n// Hybrid approach - Use functions if available, fallback to direct calls\nasync function getHybridDashboardData(userId) {\n    try {\n        // Try optimized function first\n        return await getOptimizedDashboardData(userId);\n    } catch (error) {\n        console.warn('⚠️ Optimized function failed, falling back to direct calls');\n        // Fallback to original dataService functions\n        const { getUserData, getWalletData, getVideoCountData } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n        const [userData, walletData, videoData] = await Promise.all([\n            getUserData(userId),\n            getWalletData(userId),\n            getVideoCountData(userId)\n        ]);\n        return {\n            userData,\n            walletData,\n            videoData\n        };\n    }\n}\n// Admin Dashboard Stats - Server-side aggregation\nasync function getOptimizedAdminDashboardStats() {\n    try {\n        console.log('🚀 Using optimized admin dashboard stats function...');\n        const result = await getAdminDashboardStatsFunction({});\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Admin dashboard stats loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from admin dashboard stats function');\n    } catch (error) {\n        console.error('❌ Error in optimized admin dashboard stats:', error);\n        throw error;\n    }\n}\n// Admin Users - Server-side filtering and pagination\nasync function getOptimizedAdminUsers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        console.log('🚀 Using optimized admin users function...');\n        const result = await getAdminUsersFunction(options);\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Admin users loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from admin users function');\n    } catch (error) {\n        console.error('❌ Error in optimized admin users:', error);\n        throw error;\n    }\n}\n// Admin Notifications - Optimized loading\nasync function getOptimizedAdminNotifications() {\n    let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'all';\n    try {\n        console.log('🚀 Using optimized admin notifications function...');\n        const result = await getAdminNotificationsFunction({\n            limit,\n            type\n        });\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Admin notifications loaded via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from admin notifications function');\n    } catch (error) {\n        console.error('❌ Error in optimized admin notifications:', error);\n        throw error;\n    }\n}\n// Create Admin Notification - Optimized creation\nasync function createOptimizedAdminNotification(notificationData) {\n    try {\n        console.log('🚀 Using optimized admin notification creation...');\n        const result = await createAdminNotificationFunction(notificationData);\n        if (result.data && typeof result.data === 'object' && 'success' in result.data) {\n            const data = result.data;\n            if (data.success) {\n                console.log('✅ Admin notification created via optimized function');\n                return data.data;\n            }\n        }\n        throw new Error('Invalid response from admin notification creation function');\n    } catch (error) {\n        console.error('❌ Error in optimized admin notification creation:', error);\n        throw error;\n    }\n}\n// Hybrid admin dashboard stats with fallback\nasync function getHybridAdminDashboardStats() {\n    try {\n        // Try optimized function first\n        return await getOptimizedAdminDashboardStats();\n    } catch (error) {\n        console.warn('⚠️ Optimized admin stats function failed, falling back to direct calls');\n        // Fallback to original adminDataService\n        const { getAdminDashboardStats } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_adminDataService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./adminDataService */ \"(app-pages-browser)/./src/lib/adminDataService.ts\"));\n        return await getAdminDashboardStats();\n    }\n}\n// Export optimized functions with fallback capability\nconst optimizedService = {\n    // User functions\n    getDashboardData: getHybridDashboardData,\n    submitVideoBatch: submitOptimizedVideoBatch,\n    processWithdrawal: processOptimizedWithdrawal,\n    getUserNotifications: getOptimizedUserNotifications,\n    getUserTransactions: getOptimizedUserTransactions,\n    // Admin functions\n    getAdminWithdrawals: getOptimizedAdminWithdrawals,\n    getAdminDashboardStats: getHybridAdminDashboardStats,\n    getAdminUsers: getOptimizedAdminUsers,\n    getAdminNotifications: getOptimizedAdminNotifications,\n    createAdminNotification: createOptimizedAdminNotification,\n    // Utility\n    areFunctionsAvailable\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/optimizedDataService.ts\n"));

/***/ })

});