/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/setup/page";
exports.ids = ["app/admin/setup/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fsetup%2Fpage&page=%2Fadmin%2Fsetup%2Fpage&appPaths=%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fsetup%2Fpage&page=%2Fadmin%2Fsetup%2Fpage&appPaths=%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/setup/page.tsx */ \"(rsc)/./src/app/admin/setup/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'setup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/setup/page\",\n        pathname: \"/admin/setup\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fsetup%2Fpage&page=%2Fadmin%2Fsetup%2Fpage&appPaths=%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/setup/page.tsx */ \"(rsc)/./src/app/admin/setup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDc2V0dXAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxzZXR1cFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/admin/setup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin/setup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\setup\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f1be16949bb3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYxYmUxNjk0OWJiM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'MyTube - Watch Videos & Earn',\n    description: 'Watch videos and earn money. Complete daily video watching tasks to earn rewards.',\n    keywords: 'video watching, earn money, online earning, video tasks, rewards',\n    authors: [\n        {\n            name: 'MyTube Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/mytube-favicon.svg',\n        apple: '/img/mytube-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#FF0000'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading MyTube...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgTXlUdWJlLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/setup/page.tsx */ \"(ssr)/./src/app/admin/setup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDc2V0dXAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxzZXR1cFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/admin/setup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin/setup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSetupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/dataService */ \"(ssr)/./src/lib/dataService.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction AdminSetupPage() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setupComplete, setSetupComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const createAdminAccount = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check if admin already exists\n            const adminEmail = '<EMAIL>';\n            const adminPassword = '123456';\n            // Try to create the admin user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth, adminEmail, adminPassword);\n            const user = userCredential.user;\n            // Create admin user document in Firestore\n            const adminData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.name]: 'MyTube Admin',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.email]: adminEmail,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.mobile]: '**********',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.referralCode]: 'ADMIN001',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.referredBy]: '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.plan]: 'Admin',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.activeDays]: 999999,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.status]: 'active',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_7__.FIELD_NAMES.videoDuration]: 300,\n                // Admin specific fields\n                role: 'admin',\n                isAdmin: true,\n                permissions: [\n                    'all'\n                ]\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_7__.COLLECTIONS.users, user.uid), adminData);\n            // Create admin document in admins collection for security rules\n            const adminDoc = {\n                email: adminEmail,\n                name: 'MyTube Admin',\n                role: 'super_admin',\n                permissions: [\n                    'all'\n                ],\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                isActive: true\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, 'admins', user.uid), adminDoc);\n            // Sign out the admin user (they'll need to login properly)\n            await _lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth.signOut();\n            setSetupComplete(true);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8__[\"default\"].fire({\n                icon: 'success',\n                title: 'Admin Account Created!',\n                html: `\n          <div class=\"text-left\">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> 123456</p>\n            <br>\n            <p>The admin account has been successfully created. You can now login using these credentials.</p>\n          </div>\n        `,\n                confirmButtonText: 'Go to Admin Login'\n            }).then(()=>{\n                window.location.href = '/admin/login';\n            });\n        } catch (error) {\n            console.error('Error creating admin account:', error);\n            let message = 'Failed to create admin account';\n            if (error.code === 'auth/email-already-in-use') {\n                message = 'Admin account already exists! You can login with: <EMAIL> / 123456';\n                sweetalert2__WEBPACK_IMPORTED_MODULE_8__[\"default\"].fire({\n                    icon: 'info',\n                    title: 'Admin Account Exists',\n                    html: `\n            <div class=\"text-left\">\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Password:</strong> 123456</p>\n              <br>\n              <p>The admin account already exists. Use these credentials to login.</p>\n            </div>\n          `,\n                    confirmButtonText: 'Go to Admin Login'\n                }).then(()=>{\n                    window.location.href = '/admin/login';\n                });\n            } else {\n                sweetalert2__WEBPACK_IMPORTED_MODULE_8__[\"default\"].fire({\n                    icon: 'error',\n                    title: 'Setup Failed',\n                    text: message\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card w-full max-w-md p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Admin Setup\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80\",\n                            children: \"Create the admin account for MyTube\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                !setupComplete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500/20 rounded-lg p-4 border border-blue-500/30\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-info-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Admin Account Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white/80 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Password:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" 123456\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Role:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" Super Administrator\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: createAdminAccount,\n                            disabled: isLoading,\n                            className: \"w-full btn-primary flex items-center justify-center\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Creating Admin Account...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-user-shield mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Create Admin Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start text-yellow-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-exclamation-triangle mr-2 mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold mb-1\",\n                                                children: \"Security Notice:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"This will create an admin account with full system access. Make sure to change the password after first login for security.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 text-6xl mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-check-circle\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Setup Complete!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-6\",\n                            children: \"Admin account has been created successfully.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/login\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sign-in-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                \"Go to Admin Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/login\",\n                            className: \"text-white/80 hover:text-white transition-colors inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sign-in-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                \"Admin Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-arrow-left mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/setup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   calculateUserActiveDays: () => (/* binding */ calculateUserActiveDays),\n/* harmony export */   checkAndRunDailyProcess: () => (/* binding */ checkAndRunDailyProcess),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   dailyActiveDaysIncrement: () => (/* binding */ dailyActiveDaysIncrement),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   fixAllUsersActiveDays: () => (/* binding */ fixAllUsersActiveDays),\n/* harmony export */   forceDailyProcessCatchup: () => (/* binding */ forceDailyProcessCatchup),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getLiveActiveDays: () => (/* binding */ getLiveActiveDays),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   migrateQuickVideoAdvantageSystem: () => (/* binding */ migrateQuickVideoAdvantageSystem),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   recalculateAllUsersActiveDays: () => (/* binding */ recalculateAllUsersActiveDays),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   resetAllUsersLastUpdate: () => (/* binding */ resetAllUsersLastUpdate),\n/* harmony export */   resetDailyVideoCount: () => (/* binding */ resetDailyVideoCount),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   submitBatchVideos: () => (/* binding */ submitBatchVideos),\n/* harmony export */   updateUserActiveDays: () => (/* binding */ updateUserActiveDays),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageRemainingDays: 'quickVideoAdvantageRemainingDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Admin control fields\n    manuallySetActiveDays: 'manuallySetActiveDays',\n    lastActiveDaysUpdate: 'lastActiveDaysUpdate',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageRemainingDays: Number(data[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            let todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            // If it's a new day, reset todayVideos and update active days\n            if (isNewDay && todayVideos > 0) {\n                console.log(`🔄 Resetting daily video count for user ${userId} (was ${todayVideos})`);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.todayVideos]: 0\n                });\n                todayVideos = 0;\n                // Also update active days for accurate tracking\n                try {\n                    await updateUserActiveDays(userId);\n                } catch (error) {\n                    console.error('Error updating active days during daily reset:', error);\n                }\n                // Trigger centralized daily process check (runs regardless of user login)\n                try {\n                    await checkAndRunDailyProcess();\n                } catch (error) {\n                    console.error('Error in daily process check:', error);\n                }\n            }\n            return {\n                totalVideos,\n                todayVideos,\n                remainingVideos: Math.max(0, 50 - todayVideos)\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count (single video)\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        // Get current data to check if we need to reset daily count\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            // Check if it's a new day\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            if (isNewDay && currentTodayVideos > 0) {\n                // Reset today's count and then increment\n                console.log(`🔄 Resetting and updating daily video count for user ${userId}`);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayVideos]: 1,\n                    [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            } else {\n                // Normal increment\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            }\n        } else {\n            // User doesn't exist, create with initial values\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n            });\n        }\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Batch video submission (for submitting exactly 50 videos at once)\nasync function submitBatchVideos(userId, videoCount = 50) {\n    try {\n        if (videoCount !== 50) {\n            throw new Error(`Invalid batch size: ${videoCount}. Expected exactly 50 videos.`);\n        }\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        // Get current data to validate submission\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (!userDoc.exists()) {\n            throw new Error('User not found');\n        }\n        const data = userDoc.data();\n        const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n        const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0;\n        const currentTotalVideos = data[FIELD_NAMES.totalVideos] || 0;\n        // Check if user already submitted today\n        if (currentTodayVideos >= 50) {\n            throw new Error('Daily video limit already reached. Cannot submit more videos today.');\n        }\n        // Check if it's a new day\n        const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n        let newTodayVideos;\n        let newTotalVideos;\n        if (isNewDay && currentTodayVideos > 0) {\n            // Reset today's count and set to 50\n            console.log(`🔄 Resetting daily count and submitting batch for user ${userId}`);\n            newTodayVideos = 50;\n            newTotalVideos = currentTotalVideos + 50;\n        } else {\n            // Add to existing count (should be 0 + 50 = 50 for first submission)\n            newTodayVideos = Math.min(currentTodayVideos + 50, 50) // Cap at 50\n            ;\n            newTotalVideos = currentTotalVideos + 50;\n        }\n        // Atomic update - all fields updated together\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: newTotalVideos,\n            [FIELD_NAMES.todayVideos]: newTodayVideos,\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n        console.log(`✅ Batch submission successful for user ${userId}: +50 videos (Total: ${newTotalVideos}, Today: ${newTodayVideos})`);\n        return {\n            totalVideos: newTotalVideos,\n            todayVideos: newTodayVideos,\n            videosAdded: 50\n        };\n    } catch (error) {\n        console.error('Error submitting batch videos:', error);\n        throw error;\n    }\n}\n// Reset daily video count for a user (admin function)\nasync function resetDailyVideoCount(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.todayVideos]: 0\n        });\n        console.log(`✅ Reset daily video count for user ${userId}`);\n    } catch (error) {\n        console.error('Error resetting daily video count:', error);\n        throw error;\n    }\n}\n// Get live active days for user display (always returns current value from database)\nasync function getLiveActiveDays(userId) {\n    try {\n        console.log(`🔍 Getting live active days for user ${userId}`);\n        // Get the current active days from database (this is updated by daily process)\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.error('User data not found for live active days:', userId);\n            return 1;\n        }\n        const activeDays = userData.activeDays || 1;\n        console.log(`📊 Live active days for user ${userId}: ${activeDays}`);\n        return activeDays;\n    } catch (error) {\n        console.error('Error getting live active days:', error);\n        return 1;\n    }\n}\n// Centralized Active Days Calculation (simplified and reliable)\nasync function calculateUserActiveDays(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.error('User data not found for active days calculation:', userId);\n            return 1;\n        }\n        const today = new Date();\n        let activeDays = 1 // Always start with 1\n        ;\n        if (userData.plan === 'Trial') {\n            // For trial users, calculate based on joined date (start from 1)\n            const joinedDate = userData.joinedDate || new Date();\n            const daysDifference = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            activeDays = Math.max(1, daysDifference + 1) // Day 0 = 1 active day, Day 1 = 2 active days, etc.\n            ;\n        } else {\n            // For paid plans, calculate from plan activation date excluding leave days\n            const planActivationDate = userData.planExpiry ? new Date(userData.planExpiry.getTime() - getPlanValidityDays(userData.plan) * 24 * 60 * 60 * 1000) : userData.joinedDate || new Date();\n            const daysSincePlanActivated = Math.floor((today.getTime() - planActivationDate.getTime()) / (1000 * 60 * 60 * 24));\n            // Get leave days count\n            const { isAdminLeaveDay, isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n            let totalLeaveDays = 0;\n            // Count admin leave days since plan activation\n            for(let i = 0; i <= daysSincePlanActivated; i++){\n                const checkDate = new Date(planActivationDate.getTime() + i * 24 * 60 * 60 * 1000);\n                const isAdminLeave = await isAdminLeaveDay(checkDate);\n                const isUserLeave = await isUserOnLeave(userId, checkDate);\n                if (isAdminLeave || isUserLeave) {\n                    totalLeaveDays++;\n                }\n            }\n            // Calculate active days: Days since plan activated - leave days + 1 (for activation day)\n            activeDays = Math.max(1, daysSincePlanActivated - totalLeaveDays + 1);\n        }\n        return activeDays;\n    } catch (error) {\n        console.error('Error calculating user active days:', error);\n        return 1 // Return 1 on error to ensure minimum value\n        ;\n    }\n}\n// Update user's active days (respects manual admin settings)\nasync function updateUserActiveDays(userId, forceUpdate = false) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.error('User data not found for active days update:', userId);\n            return 1;\n        }\n        // Check if active days were manually set by admin (skip auto-calculation unless forced)\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        const userDocData = userDoc.data();\n        const manuallySetActiveDays = userDocData?.manuallySetActiveDays || false;\n        if (manuallySetActiveDays && !forceUpdate) {\n            console.log(`⏭️ Skipping active days auto-update for user ${userId} - manually set by admin (current: ${userData.activeDays})`);\n            return userData.activeDays || 1;\n        }\n        let newActiveDays;\n        if (manuallySetActiveDays && forceUpdate) {\n            // Even when forced, if manually set, don't recalculate - keep current value\n            console.log(`⚠️ Force update requested but active days manually set for user ${userId} - keeping current value`);\n            newActiveDays = userData.activeDays || 1;\n        } else {\n            // Calculate correct active days using centralized function only for auto-calculated users\n            newActiveDays = await calculateUserActiveDays(userId);\n        }\n        // Only update if the value has changed\n        const currentActiveDays = userData.activeDays || 0;\n        if (newActiveDays !== currentActiveDays) {\n            console.log(`📅 Updating active days for user ${userId}: ${currentActiveDays} → ${newActiveDays}`);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: newActiveDays\n            });\n        }\n        return newActiveDays;\n    } catch (error) {\n        console.error('Error updating user active days:', error);\n        throw error;\n    }\n}\n// Centralized daily process checker (runs regardless of user login)\nasync function checkAndRunDailyProcess() {\n    try {\n        const today = new Date().toDateString();\n        // Check Firestore for reliable cross-user tracking\n        const globalResetDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'dailyReset'));\n        const lastProcessDate = globalResetDoc.exists() ? globalResetDoc.data()?.lastResetDate : null;\n        if (!lastProcessDate || lastProcessDate !== today) {\n            // Calculate how many days we missed\n            let daysMissed = 1;\n            if (lastProcessDate) {\n                const lastDate = new Date(lastProcessDate);\n                const timeDiff = new Date().getTime() - lastDate.getTime();\n                daysMissed = Math.floor(timeDiff / (1000 * 60 * 60 * 24));\n            }\n            console.log(`🌅 Running daily process for all users (${daysMissed} day(s) catchup)...`);\n            // Run the daily process (this handles the +1 increment for all users)\n            const result = await dailyActiveDaysIncrement();\n            // Update Firestore tracking\n            try {\n                const globalResetDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'dailyReset');\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(globalResetDocRef, {\n                    lastResetDate: today,\n                    lastResetTimestamp: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                    lastResult: result,\n                    daysCaughtUp: daysMissed,\n                    triggeredBy: 'automatic_daily_check'\n                }, {\n                    merge: true\n                });\n                console.log(`✅ Daily process completed for ${daysMissed} day(s):`, result);\n            } catch (trackingError) {\n                console.error('Error updating daily process tracking:', trackingError);\n            }\n            return result;\n        } else {\n            console.log('⏭️ Daily process already completed today');\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in centralized daily process:', error);\n        throw error;\n    }\n}\n// Daily active days increment for all users (runs automatically every day)\nasync function dailyActiveDaysIncrement() {\n    try {\n        console.log('🌅 Starting daily active days increment...');\n        const today = new Date();\n        const todayDateString = today.toDateString();\n        // Check if today is an admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(today);\n        if (isAdminLeave) {\n            console.log('⏸️ Skipping active days increment - Admin leave day');\n            return {\n                incrementedCount: 0,\n                skippedCount: 0,\n                errorCount: 0,\n                reason: 'Admin leave day'\n            };\n        }\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let incrementedCount = 0;\n        let skippedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Check if we already updated today\n                const lastUpdate = userData[FIELD_NAMES.lastActiveDaysUpdate]?.toDate();\n                if (lastUpdate && lastUpdate.toDateString() === todayDateString) {\n                    skippedCount++;\n                    continue;\n                }\n                // Check if user is on leave today\n                const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n                const isUserLeave = await isUserOnLeave(userId, today);\n                if (isUserLeave) {\n                    console.log(`⏸️ Skipping active days increment for user ${userId} - User leave day`);\n                    // Still update the last update date to mark that we processed this user today\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), {\n                        [FIELD_NAMES.lastActiveDaysUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                    });\n                    skippedCount++;\n                    continue;\n                }\n                // Simple daily increment: +1 for all users (regardless of manual/auto setting)\n                const currentActiveDays = userData[FIELD_NAMES.activeDays] || 1;\n                const newActiveDays = currentActiveDays + 1;\n                console.log(`📅 Daily active days increment for user ${userId}: ${currentActiveDays} → ${newActiveDays}`);\n                // Prepare update data\n                const updateData = {\n                    [FIELD_NAMES.activeDays]: newActiveDays,\n                    [FIELD_NAMES.lastActiveDaysUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                };\n                // Handle quick video advantage decrement\n                const currentQuickAdvantage = userData[FIELD_NAMES.quickVideoAdvantage] || false;\n                const currentRemainingDays = userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0;\n                if (currentQuickAdvantage && currentRemainingDays > 0) {\n                    const newRemainingDays = currentRemainingDays - 1;\n                    updateData[FIELD_NAMES.quickVideoAdvantageRemainingDays] = newRemainingDays;\n                    // If remaining days reach 0, disable quick video advantage\n                    if (newRemainingDays <= 0) {\n                        updateData[FIELD_NAMES.quickVideoAdvantage] = false;\n                        updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null;\n                        console.log(`⏰ Quick video advantage expired for user ${userId}`);\n                        // Add transaction record for expiry\n                        try {\n                            await addTransaction(userId, {\n                                type: 'quick_advantage_expired',\n                                amount: 0,\n                                description: 'Quick video advantage expired (time limit reached)'\n                            });\n                        } catch (transactionError) {\n                            console.error('Error adding expiry transaction:', transactionError);\n                        }\n                    } else {\n                        console.log(`⏰ Quick video advantage for user ${userId}: ${currentRemainingDays} → ${newRemainingDays} days remaining`);\n                    }\n                }\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), updateData);\n                incrementedCount++;\n                console.log(`📅 Updated active days for user ${userId}: ${currentActiveDays} → ${newActiveDays}`);\n            } catch (error) {\n                console.error(`Error updating active days for user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Daily active days increment completed: ${incrementedCount} incremented, ${skippedCount} skipped, ${errorCount} errors`);\n        return {\n            incrementedCount,\n            skippedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error in daily active days increment:', error);\n        throw error;\n    }\n}\n// Migrate quick video advantage from expiry date to remaining days system\nasync function migrateQuickVideoAdvantageSystem() {\n    try {\n        console.log('🔄 Starting quick video advantage system migration...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let migratedCount = 0;\n        let skippedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Skip if user doesn't have quick video advantage\n                if (!userData[FIELD_NAMES.quickVideoAdvantage]) {\n                    skippedCount++;\n                    continue;\n                }\n                // Skip if already migrated (has remainingDays field)\n                if (userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] !== undefined) {\n                    skippedCount++;\n                    continue;\n                }\n                // Calculate remaining days from expiry date\n                let remainingDays = 0;\n                const expiry = userData[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate();\n                if (expiry) {\n                    const now = new Date();\n                    const timeDiff = expiry.getTime() - now.getTime();\n                    remainingDays = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n                }\n                // Update user with remaining days\n                const updateData = {\n                    [FIELD_NAMES.quickVideoAdvantageRemainingDays]: remainingDays\n                };\n                // If expired, disable the advantage\n                if (remainingDays <= 0) {\n                    updateData[FIELD_NAMES.quickVideoAdvantage] = false;\n                    updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null;\n                }\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), updateData);\n                migratedCount++;\n                console.log(`✅ Migrated user ${userId}: ${remainingDays} days remaining`);\n            } catch (error) {\n                console.error(`Error migrating user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Quick video advantage migration completed: ${migratedCount} migrated, ${skippedCount} skipped, ${errorCount} errors`);\n        return {\n            migratedCount,\n            skippedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error migrating quick video advantage system:', error);\n        throw error;\n    }\n}\n// Fix all users' active days (admin function)\nasync function fixAllUsersActiveDays() {\n    try {\n        console.log('🔧 Starting to fix all users active days...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let fixedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                await updateUserActiveDays(userDoc.id, true) // Force update\n                ;\n                fixedCount++;\n            } catch (error) {\n                console.error(`Error fixing active days for user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Fixed active days for ${fixedCount} users, ${errorCount} errors`);\n        return {\n            fixedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error fixing all users active days:', error);\n        throw error;\n    }\n}\n// Recalculate active days for all users using centralized calculation (admin function)\nasync function recalculateAllUsersActiveDays() {\n    try {\n        console.log('🔄 Starting to recalculate all users active days with centralized formula...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let recalculatedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                const userData = userDoc.data();\n                const userId = userDoc.id;\n                // Use centralized calculation for correct active days\n                const correctActiveDays = await calculateUserActiveDays(userId);\n                // Update only if different from current value\n                const currentActiveDays = userData.activeDays || 0;\n                if (correctActiveDays !== currentActiveDays) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), {\n                        [FIELD_NAMES.activeDays]: correctActiveDays,\n                        [FIELD_NAMES.manuallySetActiveDays]: false // Reset manual flag\n                    });\n                    console.log(`📅 Recalculated active days for user ${userId}: ${currentActiveDays} → ${correctActiveDays}`);\n                    recalculatedCount++;\n                }\n            } catch (error) {\n                console.error(`Error recalculating active days for user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Recalculated active days for ${recalculatedCount} users, ${errorCount} errors`);\n        return {\n            recalculatedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error recalculating all users active days:', error);\n        throw error;\n    }\n}\n// Force daily process to run for all missed days (admin function)\nasync function forceDailyProcessCatchup() {\n    try {\n        console.log('🚀 Starting forced daily process catchup for all users...');\n        // Get the last process date from system\n        const globalResetDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'dailyReset'));\n        const lastProcessDate = globalResetDoc.exists() ? globalResetDoc.data()?.lastResetDate : null;\n        console.log('Last daily process date:', lastProcessDate);\n        // Force run the daily process regardless of last run date\n        const result = await dailyActiveDaysIncrement();\n        // Update the system tracking to today\n        const today = new Date().toDateString();\n        const globalResetDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'dailyReset');\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(globalResetDocRef, {\n            lastResetDate: today,\n            lastResetTimestamp: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            lastResult: result,\n            forcedCatchup: true,\n            forcedCatchupTimestamp: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        }, {\n            merge: true\n        });\n        console.log('✅ Forced daily process catchup completed:', result);\n        return result;\n    } catch (error) {\n        console.error('Error in forced daily process catchup:', error);\n        throw error;\n    }\n}\n// Reset all users' lastActiveDaysUpdate to force fresh daily increment (admin function)\nasync function resetAllUsersLastUpdate() {\n    try {\n        console.log('🔄 Resetting all users lastActiveDaysUpdate field...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let resetCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                const userId = userDoc.id;\n                // Reset the lastActiveDaysUpdate field\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), {\n                    [FIELD_NAMES.lastActiveDaysUpdate]: null\n                });\n                resetCount++;\n                console.log(`✅ Reset lastActiveDaysUpdate for user ${userId}`);\n            } catch (error) {\n                console.error(`Error resetting lastActiveDaysUpdate for user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Reset lastActiveDaysUpdate for ${resetCount} users, ${errorCount} errors`);\n        return {\n            resetCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error resetting all users lastActiveDaysUpdate:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // Use live active days from database (updated by daily process)\n        const activeDays = await getLiveActiveDays(userId);\n        // If user is on Trial plan, check expiry based on active days\n        if (userData.plan === 'Trial') {\n            // Trial expires when active days >= 3 (i.e., after day 2)\n            // Days left calculation: How many days can user still work (including today)?\n            // Day 1: activeDays=1, daysLeft=2 (can work today + 1 more day = 2 total days left)\n            // Day 2: activeDays=2, daysLeft=1 (can work today only = 1 day left)\n            // Day 3: activeDays=3, daysLeft=0 (expired)\n            const trialDaysLeft = Math.max(0, 3 - activeDays);\n            return {\n                expired: activeDays >= 3,\n                reason: activeDays >= 3 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: activeDays\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: activeDays\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        // Days left calculation: How many days can user still work (including today)?\n        // Day 1: activeDays=1, daysLeft=30 (can work today + 29 more days = 30 total days left)\n        // Day 30: activeDays=30, daysLeft=1 (can work today only = 1 day left)\n        // Day 31: activeDays=31, daysLeft=0 (expired)\n        const daysLeft = Math.max(0, planValidityDays + 1 - activeDays);\n        const expired = activeDays >= planValidityDays // Expires when active days reach plan validity limit\n        ;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: activeDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`);\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`);\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage) {\n        return false;\n    }\n    // Use remaining days if available (new system), otherwise fall back to expiry date (legacy)\n    if (userData.quickVideoAdvantageRemainingDays !== undefined) {\n        return userData.quickVideoAdvantageRemainingDays > 0;\n    }\n    // Legacy fallback for existing users\n    if (userData.quickVideoAdvantageExpiry) {\n        const now = new Date();\n        return now < userData.quickVideoAdvantageExpiry;\n    }\n    return false;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageRemainingDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Quick video advantage granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageRemainingDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(`Removed quick video advantage from user ${userId}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Quick video advantage removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request with atomic operations to prevent race conditions\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Use Firestore transaction to ensure atomic operations\n        const result = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.runTransaction)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, async (transaction)=>{\n            // Get user document reference\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            const userDoc = await transaction.get(userRef);\n            if (!userDoc.exists()) {\n                throw new Error('User not found');\n            }\n            const userData = userDoc.data();\n            const currentWallet = userData[FIELD_NAMES.wallet] || 0;\n            // Check if withdrawal is allowed (this includes pending withdrawal check)\n            const withdrawalCheck = await checkWithdrawalAllowed(userId);\n            if (!withdrawalCheck.allowed) {\n                throw new Error(withdrawalCheck.reason);\n            }\n            // Check if user has sufficient balance\n            if (currentWallet < amount) {\n                throw new Error(`Insufficient wallet balance. Available: ₹${currentWallet}, Requested: ₹${amount}`);\n            }\n            // Debit the amount from user's wallet atomically\n            const newWalletBalance = currentWallet - amount;\n            transaction.update(userRef, {\n                [FIELD_NAMES.wallet]: newWalletBalance\n            });\n            // Create withdrawal document\n            const withdrawalData = {\n                userId,\n                amount,\n                bankDetails,\n                status: 'pending',\n                date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n            };\n            const withdrawalRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals));\n            transaction.set(withdrawalRef, withdrawalData);\n            // Create transaction record\n            const transactionData = {\n                userId,\n                type: 'withdrawal_request',\n                amount: -amount,\n                description: `Withdrawal request submitted - ₹${amount} debited from wallet`,\n                date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                status: 'pending',\n                balanceAfter: newWalletBalance\n            };\n            const transactionRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions));\n            transaction.set(transactionRef, transactionData);\n            return withdrawalRef.id;\n        });\n        console.log(`✅ Withdrawal request created successfully: ${result}`);\n        return result;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate unique referral code with MYN prefix and sequential numbering\nasync function generateUniqueReferralCode() {\n    try {\n        // Try to get count from server for sequential numbering\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count;\n            const sequentialNumber = (count + 1).toString().padStart(4, '0');\n            return `MYN${sequentialNumber}`;\n        } catch (countError) {\n            console.warn('Failed to get count from server, using fallback method:', countError);\n            // Fallback to timestamp-based generation\n            const timestamp = Date.now().toString().slice(-4);\n            const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n            return `MYN${timestamp}${randomPart}`;\n        }\n    } catch (error) {\n        console.error('Error generating unique referral code:', error);\n        // Final fallback\n        const timestamp = Date.now().toString().slice(-4);\n        return `MYN${timestamp}`;\n    }\n}\n// Generate sequential referral code (alias for generateUniqueReferralCode)\nasync function generateSequentialReferralCode() {\n    return generateUniqueReferralCode();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ\",\n    authDomain: \"mytube-india.firebaseapp.com\",\n    projectId: \"mytube-india\",\n    storageBucket: \"mytube-india.firebasestorage.app\",\n    messagingSenderId: \"************\",\n    appId: \"1:************:web:ebedaec6a492926af2056a\",\n    measurementId: \"G-R24C6N7CWJ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/sweetalert2","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fsetup%2Fpage&page=%2Fadmin%2Fsetup%2Fpage&appPaths=%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();