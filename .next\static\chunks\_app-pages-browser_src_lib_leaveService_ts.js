"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_leaveService_ts"],{

/***/ "(app-pages-browser)/./src/lib/leaveService.ts":
/*!*********************************!*\
  !*** ./src/lib/leaveService.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyUserLeave: () => (/* binding */ applyUserLeave),\n/* harmony export */   calculateActiveDays: () => (/* binding */ calculateActiveDays),\n/* harmony export */   cancelUserLeave: () => (/* binding */ cancelUserLeave),\n/* harmony export */   createAdminLeave: () => (/* binding */ createAdminLeave),\n/* harmony export */   debugAdminLeaveStatus: () => (/* binding */ debugAdminLeaveStatus),\n/* harmony export */   deleteAdminLeave: () => (/* binding */ deleteAdminLeave),\n/* harmony export */   getAdminLeaves: () => (/* binding */ getAdminLeaves),\n/* harmony export */   getAllUserLeaves: () => (/* binding */ getAllUserLeaves),\n/* harmony export */   getUserLeaves: () => (/* binding */ getUserLeaves),\n/* harmony export */   getUserMonthlyLeaveCount: () => (/* binding */ getUserMonthlyLeaveCount),\n/* harmony export */   isAdminLeaveDay: () => (/* binding */ isAdminLeaveDay),\n/* harmony export */   isUserOnLeave: () => (/* binding */ isUserOnLeave),\n/* harmony export */   isWorkBlocked: () => (/* binding */ isWorkBlocked),\n/* harmony export */   updateUserLeaveStatus: () => (/* binding */ updateUserLeaveStatus)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\n\nconst COLLECTIONS = {\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Admin Leave Functions\nasync function createAdminLeave(leaveData) {\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n        });\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating admin leave:', error);\n        throw error;\n    }\n}\nasync function getAdminLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'asc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                createdAt: doc.data().createdAt.toDate()\n            }));\n        console.log('📅 All admin leaves:', leaves);\n        return leaves;\n    } catch (error) {\n        console.error('Error getting admin leaves:', error);\n        throw error;\n    }\n}\n// Debug function to check current admin leave status\nasync function debugAdminLeaveStatus() {\n    try {\n        const today = new Date();\n        console.log('🔍 Debug: Checking admin leave status for today:', today.toDateString());\n        const isLeave = await isAdminLeaveDay(today);\n        console.log('📊 Debug: Admin leave result:', isLeave);\n        const allLeaves = await getAdminLeaves();\n        console.log('📅 Debug: All admin leaves in database:', allLeaves);\n        const todayLeaves = allLeaves.filter((leave)=>leave.date.toDateString() === today.toDateString());\n        console.log('📅 Debug: Today\\'s admin leaves:', todayLeaves);\n    } catch (error) {\n        console.error('❌ Debug: Error checking admin leave status:', error);\n    }\n}\nasync function deleteAdminLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves, leaveId));\n    } catch (error) {\n        console.error('Error deleting admin leave:', error);\n        throw error;\n    }\n}\nasync function isAdminLeaveDay(date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking admin leave for date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasAdminLeave = !querySnapshot.empty;\n        if (hasAdminLeave) {\n            console.log('📅 Found admin leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('📅 No admin leaves found for today');\n        }\n        return hasAdminLeave;\n    } catch (error) {\n        console.error('❌ Error checking admin leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// User Leave Functions\nasync function applyUserLeave(leaveData) {\n    try {\n        // Check if user has available leave quota for automatic approval\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const currentMonth = currentDate.getMonth() + 1;\n        const usedLeaves = await getUserMonthlyLeaveCount(leaveData.userId, currentYear, currentMonth);\n        const maxLeaves = 4 // Monthly leave quota\n        ;\n        // Determine status and approval details\n        let status = 'pending';\n        let reviewedBy;\n        let reviewedAt = undefined;\n        let reviewNotes;\n        // Auto-approve if user has available quota\n        if (usedLeaves < maxLeaves) {\n            status = 'approved';\n            reviewedBy = 'system';\n            reviewedAt = firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now();\n            reviewNotes = \"Auto-approved: \".concat(usedLeaves + 1, \"/\").concat(maxLeaves, \" monthly leaves used\");\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            status,\n            appliedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            ...reviewedBy && {\n                reviewedBy\n            },\n            ...reviewedAt && {\n                reviewedAt\n            },\n            ...reviewNotes && {\n                reviewNotes\n            }\n        });\n        return {\n            id: docRef.id,\n            autoApproved: status === 'approved',\n            usedLeaves: usedLeaves + (status === 'approved' ? 1 : 0),\n            maxLeaves\n        };\n    } catch (error) {\n        console.error('Error applying user leave:', error);\n        throw error;\n    }\n}\nasync function getUserLeaves(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>{\n            var _doc_data_reviewedAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: (_doc_data_reviewedAt = doc.data().reviewedAt) === null || _doc_data_reviewedAt === void 0 ? void 0 : _doc_data_reviewedAt.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting user leaves:', error);\n        throw error;\n    }\n}\n// Get all user leaves for admin review\nasync function getAllUserLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('appliedAt', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>{\n            var _doc_data_reviewedAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: (_doc_data_reviewedAt = doc.data().reviewedAt) === null || _doc_data_reviewedAt === void 0 ? void 0 : _doc_data_reviewedAt.toDate()\n            };\n        });\n        // Get user details for each leave\n        const { getUserData } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n        for (const leave of leaves){\n            try {\n                const userData = await getUserData(leave.userId);\n                if (userData) {\n                    leave.userName = userData.name;\n                    leave.userEmail = userData.email;\n                }\n            } catch (error) {\n                console.error(\"Error getting user data for \".concat(leave.userId, \":\"), error);\n                leave.userName = 'Unknown User';\n                leave.userEmail = '<EMAIL>';\n            }\n        }\n        return leaves;\n    } catch (error) {\n        console.error('Error getting all user leaves:', error);\n        throw error;\n    }\n}\nasync function updateUserLeaveStatus(leaveId, status, reviewedBy, reviewNotes) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId), {\n            status,\n            reviewedBy,\n            reviewedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            reviewNotes: reviewNotes || ''\n        });\n    } catch (error) {\n        console.error('Error updating user leave status:', error);\n        throw error;\n    }\n}\nasync function cancelUserLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId));\n    } catch (error) {\n        console.error('Error cancelling user leave:', error);\n        throw error;\n    }\n}\nasync function getUserMonthlyLeaveCount(userId, year, month) {\n    try {\n        const startOfMonth = new Date(year, month - 1, 1);\n        const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfMonth)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfMonth)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.size;\n    } catch (error) {\n        console.error('Error getting user monthly leave count:', error);\n        return 0;\n    }\n}\nasync function isUserOnLeave(userId, date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking user leave for user:', userId, 'on date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasUserLeave = !querySnapshot.empty;\n        if (hasUserLeave) {\n            console.log('👤 Found user leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('👤 No user leaves found for today');\n        }\n        return hasUserLeave;\n    } catch (error) {\n        console.error('❌ Error checking user leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// Legacy function - now redirects to centralized calculation\n// This function is kept for backward compatibility but should not be used for new code\nasync function calculateActiveDays(userId, planActivatedDate) {\n    console.warn('⚠️ Using legacy calculateActiveDays function. Please use calculateUserActiveDays from dataService instead.');\n    // Import and use the centralized calculation\n    const { calculateUserActiveDays } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n    return await calculateUserActiveDays(userId);\n}\n// Check if work/withdrawals should be blocked\nasync function isWorkBlocked(userId) {\n    try {\n        const today = new Date();\n        console.log('🔍 Checking work block status for user:', userId, 'on date:', today.toDateString());\n        // Check admin leave with detailed logging\n        try {\n            const isAdminLeave = await isAdminLeaveDay(today);\n            console.log('📅 Admin leave check result:', isAdminLeave);\n            if (isAdminLeave) {\n                console.log('🚫 Work blocked due to admin leave');\n                return {\n                    blocked: true,\n                    reason: 'System maintenance/holiday'\n                };\n            }\n        } catch (adminLeaveError) {\n            console.error('❌ Error checking admin leave (allowing work to continue):', adminLeaveError);\n        // Don't block work if admin leave check fails\n        }\n        // Check user leave with detailed logging\n        try {\n            const isUserLeave = await isUserOnLeave(userId, today);\n            console.log('👤 User leave check result:', isUserLeave);\n            if (isUserLeave) {\n                console.log('🚫 Work blocked due to user leave');\n                return {\n                    blocked: true,\n                    reason: 'You are on approved leave today'\n                };\n            }\n        } catch (userLeaveError) {\n            console.error('❌ Error checking user leave (allowing work to continue):', userLeaveError);\n        // Don't block work if user leave check fails\n        }\n        console.log('✅ Work is not blocked');\n        return {\n            blocked: false\n        };\n    } catch (error) {\n        console.error('❌ Error checking work block status (allowing work to continue):', error);\n        return {\n            blocked: false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/leaveService.ts\n"));

/***/ })

}]);