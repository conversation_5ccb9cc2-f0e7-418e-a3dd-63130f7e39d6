"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./src/app/wallet/page.tsx":
/*!*********************************!*\
  !*** ./src/app/wallet/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _lib_optimizedDataService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/optimizedDataService */ \"(app-pages-browser)/./src/lib/optimizedDataService.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    const [walletData, setWalletData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWithdrawing, setIsWithdrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Bank details state\n    const [bankDetails, setBankDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBankForm, setShowBankForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bankFormData, setBankFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        accountHolderName: '',\n        accountNumber: '',\n        ifscCode: '',\n        bankName: ''\n    });\n    const [isSavingBank, setIsSavingBank] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalAllowed, setWithdrawalAllowed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        allowed: true\n    });\n    const [checkingWithdrawal, setCheckingWithdrawal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            // Trigger daily process check on wallet page load\n            const triggerDailyProcess = {\n                \"WalletPage.useEffect.triggerDailyProcess\": async ()=>{\n                    try {\n                        const { checkAndRunDailyProcess } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n                        await checkAndRunDailyProcess();\n                    } catch (error) {\n                        console.error('Daily process trigger failed:', error);\n                    }\n                }\n            }[\"WalletPage.useEffect.triggerDailyProcess\"];\n            triggerDailyProcess();\n            if (user) {\n                loadWalletData();\n                loadBankDetails();\n                loadUserData();\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        user\n    ]);\n    // Monitor leave status changes and update withdrawal eligibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (isLeaveBlocked) {\n                setWithdrawalAllowed({\n                    allowed: false,\n                    reason: leaveStatus.reason || 'Withdrawals are not available due to leave.'\n                });\n            } else if (user) {\n                // Re-check withdrawal eligibility when leave status changes\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        isLeaveBlocked,\n        leaveStatus,\n        user\n    ]);\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const checkWithdrawalEligibility = async ()=>{\n        if (!user) return;\n        try {\n            setCheckingWithdrawal(true);\n            const result = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.checkWithdrawalAllowed)(user.uid);\n            setWithdrawalAllowed(result);\n        } catch (error) {\n            console.error('Error checking withdrawal eligibility:', error);\n            setWithdrawalAllowed({\n                allowed: false,\n                reason: 'Unable to verify withdrawal eligibility. Please try again.'\n            });\n        } finally{\n            setCheckingWithdrawal(false);\n        }\n    };\n    const loadWalletData = async ()=>{\n        try {\n            setDataLoading(true);\n            // Use optimized functions for better performance\n            try {\n                console.log('🚀 Loading wallet data with optimized functions...');\n                const [walletResult, transactionsResult] = await Promise.all([\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                    _lib_optimizedDataService__WEBPACK_IMPORTED_MODULE_7__.optimizedService.getUserTransactions(user.uid, 20, 'withdrawal_request')\n                ]);\n                setWalletData(walletResult);\n                // Convert transactions to expected format\n                const formattedTransactions = transactionsResult.map((transaction)=>({\n                        id: transaction.id,\n                        type: transaction.type,\n                        amount: transaction.type === 'withdrawal_request' ? -Math.abs(transaction.amount) : transaction.amount,\n                        description: transaction.description,\n                        date: transaction.date,\n                        status: transaction.status || 'completed'\n                    }));\n                setTransactions(formattedTransactions);\n                console.log('✅ Wallet data loaded via optimized functions', formattedTransactions);\n            } catch (optimizedError) {\n                console.warn('⚠️ Optimized functions failed, using fallback:', optimizedError);\n                // Fallback to original method\n                const [walletResult, withdrawalsResult] = await Promise.all([\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                    (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserWithdrawals)(user.uid, 20)\n                ]);\n                setWalletData(walletResult);\n                // Convert withdrawals to transaction format for display\n                const withdrawalTransactions = withdrawalsResult.map((withdrawal)=>({\n                        id: withdrawal.id,\n                        type: 'withdrawal',\n                        amount: -withdrawal.amount,\n                        description: \"Withdrawal request - ₹\".concat(withdrawal.amount),\n                        date: withdrawal.date,\n                        status: withdrawal.status\n                    }));\n                setTransactions(withdrawalTransactions);\n            }\n        } catch (error) {\n            console.error('Error loading wallet data:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: 'Failed to load wallet data. Please try again.'\n            });\n        } finally{\n            setDataLoading(false);\n        }\n    };\n    const loadBankDetails = async ()=>{\n        try {\n            const bankData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getBankDetails)(user.uid);\n            setBankDetails(bankData);\n            if (bankData) {\n                setBankFormData(bankData);\n            }\n        } catch (error) {\n            console.error('Error loading bank details:', error);\n        }\n    };\n    const handleBankFormSubmit = async (e)=>{\n        e.preventDefault();\n        // Prevent multiple submissions\n        if (isSavingBank) return;\n        try {\n            setIsSavingBank(true);\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.saveBankDetails)(user.uid, bankFormData);\n            setBankDetails(bankFormData);\n            setShowBankForm(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Bank Details Saved',\n                text: 'Your bank details have been saved successfully',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } catch (error) {\n            console.error('Error saving bank details:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: error.message || 'Failed to save bank details. Please try again.'\n            });\n        } finally{\n            setIsSavingBank(false);\n        }\n    };\n    const handleBankFormChange = (e)=>{\n        const { name, value } = e.target;\n        setBankFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleWithdraw = async ()=>{\n        // Prevent multiple clicks with strict checking\n        if (isWithdrawing) {\n            console.log('⚠️ Withdrawal already in progress, ignoring duplicate request');\n            return;\n        }\n        // Check if plan is expired based on active days\n        try {\n            const { isUserPlanExpired } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n            const planStatus = await isUserPlanExpired(user.uid);\n            if (planStatus.expired) {\n                sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                    icon: 'error',\n                    title: 'Plan Expired',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-2\">Your plan has expired and you cannot make withdrawals.</p>\\n              <p class=\"text-sm text-gray-600 mb-2\">'.concat(planStatus.reason, '</p>\\n              <p class=\"text-sm text-blue-600\">Please upgrade your plan to continue using withdrawal services.</p>\\n            </div>\\n          '),\n                    confirmButtonText: 'Go to Plans',\n                    showCancelButton: true,\n                    cancelButtonText: 'OK'\n                }).then((result)=>{\n                    if (result.isConfirmed) {\n                        window.location.href = '/plans';\n                    }\n                });\n                return;\n            }\n        } catch (error) {\n            console.error('Error checking plan expiry:', error);\n        }\n        // Check if withdrawals are blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'warning',\n                title: 'Withdrawal Not Available',\n                text: leaveStatus.reason || 'Withdrawals are not available due to leave.',\n                confirmButtonText: 'OK'\n            });\n            return;\n        }\n        const amount = parseFloat(withdrawAmount);\n        if (!amount || amount <= 0) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Invalid Amount',\n                text: 'Please enter a valid amount to withdraw'\n            });\n            return;\n        }\n        if (amount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Minimum Withdrawal',\n                text: 'Minimum withdrawal amount is ₹50'\n            });\n            return;\n        }\n        if (amount > ((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Insufficient Balance',\n                text: 'You do not have enough balance in your wallet'\n            });\n            return;\n        }\n        if (!bankDetails) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'warning',\n                title: 'Bank Details Required',\n                text: 'Please add your bank details before making a withdrawal'\n            });\n            return;\n        }\n        try {\n            // Set loading state immediately to prevent race conditions\n            setIsWithdrawing(true);\n            console.log(\"\\uD83D\\uDD04 Processing withdrawal request for ₹\".concat(amount));\n            // Create withdrawal request in Firestore with atomic operations\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.createWithdrawalRequest)(user.uid, amount, bankDetails);\n            // Reload wallet data to show updated transactions\n            await loadWalletData();\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Withdrawal Request Submitted',\n                text: \"Your withdrawal request for ₹\".concat(amount, \" has been submitted and will be processed within 24-48 hours.\")\n            });\n            setWithdrawAmount('');\n            console.log(\"✅ Withdrawal request completed successfully\");\n        } catch (error) {\n            console.error('❌ Error processing withdrawal:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Withdrawal Failed',\n                text: error.message || 'Failed to process withdrawal request. Please try again.'\n            });\n        } finally{\n            setIsWithdrawing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        if (amount === undefined || amount === null || isNaN(amount)) {\n            return '₹0.00';\n        }\n        return \"₹\".concat(amount.toFixed(2));\n    };\n    if (loading || dataLoading || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading wallet...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 354,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 368,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-arrow-left mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"My Wallet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadWalletData,\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sync-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-semibold text-white\",\n                                children: \"My Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-wallet text-green-400 text-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-4xl font-bold text-green-400 mb-2\",\n                        children: formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60\",\n                        children: \"Total available balance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            bankDetails && !showBankForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-edit mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails && !showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-university text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"No bank details added yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 11\n                    }, this) : showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleBankFormSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Holder Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountHolderName\",\n                                                value: bankFormData.accountHolderName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account holder name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Bank Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"bankName\",\n                                                value: bankFormData.bankName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter bank name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Number *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountNumber\",\n                                                value: bankFormData.accountNumber,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account number\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"IFSC Code *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"ifscCode\",\n                                                value: bankFormData.ifscCode,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter IFSC code (e.g., SBIN0001234)\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSavingBank,\n                                        className: \"\".concat(isSavingBank ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:bg-blue-600'),\n                                        children: isSavingBank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving Bank Details...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-save mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Save Bank Details\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowBankForm(false),\n                                        className: \"btn-secondary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Holder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountHolderName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.bankName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: [\n                                                \"****\",\n                                                bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountNumber.slice(-4)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"IFSC Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.ifscCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, this),\n                            \"Withdraw Funds\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 9\n                    }, this),\n                    (userData === null || userData === void 0 ? void 0 : userData.plan) === 'Trial' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-lock text-red-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400 font-medium\",\n                                        children: \"Trial Plan Restriction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-3\",\n                                children: \"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/plans\",\n                                    className: \"btn-primary inline-block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-arrow-up mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock text-blue-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 font-medium\",\n                                        children: \"Withdrawal Timings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-2\",\n                                children: [\n                                    \"Withdrawals are only allowed between \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"10:00 AM to 6:00 PM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 50\n                                    }, this),\n                                    \" on non-leave days.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: [\n                                            \"Current time: \",\n                                            new Date().toLocaleTimeString(),\n                                            \" | Status: \",\n                                            withdrawalAllowed.allowed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-medium\",\n                                                children: \"✓ Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-medium\",\n                                                children: \"✗ Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkWithdrawalEligibility,\n                                        disabled: checkingWithdrawal,\n                                        className: \"text-blue-400 hover:text-blue-300 text-xs\",\n                                        children: checkingWithdrawal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"spinner w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this),\n                            !withdrawalAllowed.allowed && withdrawalAllowed.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this),\n                                    withdrawalAllowed.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"Please add your bank details before making a withdrawal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: withdrawAmount,\n                                        onChange: (e)=>setWithdrawAmount(e.target.value),\n                                        placeholder: \"Enter amount to withdraw (Min: ₹50)\",\n                                        className: \"form-input flex-1\",\n                                        min: \"50\",\n                                        max: (walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleWithdraw,\n                                        disabled: isWithdrawing || !withdrawAmount || !withdrawalAllowed.allowed || parseFloat(withdrawAmount) <= 0,\n                                        className: \"whitespace-nowrap \".concat(isWithdrawing ? 'btn-disabled cursor-not-allowed opacity-50 pointer-events-none' : withdrawalAllowed.allowed && withdrawAmount && parseFloat(withdrawAmount) > 0 ? 'btn-primary hover:bg-blue-600' : 'btn-disabled cursor-not-allowed opacity-50'),\n                                        style: {\n                                            pointerEvents: isWithdrawing ? 'none' : 'auto'\n                                        },\n                                        children: isWithdrawing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing Withdrawal...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-download mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Withdraw ₹\",\n                                                withdrawAmount || '0'\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 text-sm mt-2\",\n                                children: [\n                                    \"Available: \",\n                                    formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0),\n                                    \" | Minimum: ₹50\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 551,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-money-bill-wave mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Withdrawal History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadWalletData,\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-sync-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-2\",\n                                children: \"No withdrawal requests yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/40 text-sm\",\n                                children: \"Your withdrawal requests will appear here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/10 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-money-bill-wave text-red-400 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: transaction.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: [\n                                                            transaction.date.toLocaleDateString(),\n                                                            \" at \",\n                                                            transaction.date.toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-red-400\",\n                                                children: formatCurrency(Math.abs(transaction.amount))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs px-2 py-1 rounded-full \".concat(transaction.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' : transaction.status === 'approved' ? 'bg-green-500/20 text-green-400' : transaction.status === 'rejected' ? 'bg-red-500/20 text-red-400' : transaction.status === 'completed' ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'),\n                                                children: transaction.status === 'pending' ? '⏳ Pending' : transaction.status === 'approved' ? '✅ Approved' : transaction.status === 'rejected' ? '❌ Rejected' : transaction.status === 'completed' ? '✅ Completed' : transaction.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 672,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"VsQSNUzb5ENRMUCOOCNTvy+okCc=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wallet/page.tsx\n"));

/***/ })

});