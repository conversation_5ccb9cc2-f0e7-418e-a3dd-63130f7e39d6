"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_versionService_ts-_d5ae1"],{

/***/ "(app-pages-browser)/./src/lib/versionService.ts":
/*!***********************************!*\
  !*** ./src/lib/versionService.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkVersionAndClearCache: () => (/* binding */ checkVersionAndClearCache),\n/* harmony export */   clearApplicationCache: () => (/* binding */ clearApplicationCache),\n/* harmony export */   clearApplicationCacheSilently: () => (/* binding */ clearApplicationCacheSilently),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo),\n/* harmony export */   manualCacheClear: () => (/* binding */ manualCacheClear),\n/* harmony export */   simpleCacheClear: () => (/* binding */ simpleCacheClear),\n/* harmony export */   updateServerVersion: () => (/* binding */ updateServerVersion)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n// Version management service to handle automatic cache clearing on deployments\n\n\n// Current app version - increment this with each deployment\nconst CURRENT_VERSION = '1.1.0';\n// Version tracking in Firestore\nconst VERSION_DOC_PATH = 'system/version';\n// Local storage keys\nconst LOCAL_VERSION_KEY = 'mytube_app_version';\nconst LAST_CACHE_CLEAR_KEY = 'mytube_last_cache_clear';\n// Check if app version has changed and clear cache if needed (SILENT)\nasync function checkVersionAndClearCache() {\n    try {\n        console.log('🔍 Silently checking app version for cache management...');\n        // Get stored local version\n        const localVersion = localStorage.getItem(LOCAL_VERSION_KEY);\n        // Get server version from Firestore\n        let serverVersion = CURRENT_VERSION;\n        try {\n            const versionDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, VERSION_DOC_PATH));\n            if (versionDoc.exists()) {\n                var _versionDoc_data;\n                serverVersion = ((_versionDoc_data = versionDoc.data()) === null || _versionDoc_data === void 0 ? void 0 : _versionDoc_data.version) || CURRENT_VERSION;\n            }\n        } catch (error) {\n            console.warn('Could not fetch server version, using current version:', error);\n        }\n        // Check if version has changed\n        const versionChanged = !localVersion || localVersion !== serverVersion;\n        if (versionChanged) {\n            console.log(\"\\uD83D\\uDD04 Version changed: \".concat(localVersion || 'none', \" → \").concat(serverVersion, \" - Clearing cache silently...\"));\n            // Clear cache silently in background\n            try {\n                await clearApplicationCacheSilently();\n                // Update local version\n                localStorage.setItem(LOCAL_VERSION_KEY, serverVersion);\n                localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString());\n                console.log('✅ Cache cleared silently due to version update');\n                // Silently reload the page after a short delay to apply changes\n                setTimeout(()=>{\n                    window.location.reload();\n                }, 1000);\n                return true;\n            } catch (error) {\n                console.error('Silent cache clear failed, continuing normally:', error);\n                // Don't disrupt user experience if silent clear fails\n                localStorage.setItem(LOCAL_VERSION_KEY, serverVersion);\n                return false;\n            }\n        }\n        console.log('✅ Version unchanged, no cache clearing needed');\n        return false;\n    } catch (error) {\n        console.error('Error checking version:', error);\n        return false;\n    }\n}\n// Silent cache clearing (no user interaction, faster, background)\nasync function clearApplicationCacheSilently() {\n    try {\n        console.log('🔇 Silently clearing application cache...');\n        // Clear localStorage (except authentication data) - preserve user session\n        const authKeys = [\n            'firebase:authUser',\n            'firebase:host'\n        ];\n        const preservedData = {};\n        // Preserve auth data\n        authKeys.forEach((key)=>{\n            const keys = Object.keys(localStorage).filter((k)=>k.includes(key));\n            keys.forEach((k)=>{\n                preservedData[k] = localStorage.getItem(k);\n            });\n        });\n        // Clear all localStorage\n        localStorage.clear();\n        // Restore auth data\n        Object.entries(preservedData).forEach((param)=>{\n            let [key, value] = param;\n            if (value) {\n                localStorage.setItem(key, value);\n            }\n        });\n        // Clear sessionStorage\n        sessionStorage.clear();\n        // Clear browser cache silently (with shorter timeouts)\n        if ('caches' in window) {\n            try {\n                const cacheNames = await Promise.race([\n                    caches.keys(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache keys timeout')), 2000))\n                ]);\n                await Promise.race([\n                    Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName))),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache deletion timeout')), 2000))\n                ]);\n            } catch (error) {\n                // Silently ignore cache clearing errors\n                console.warn('Silent cache clear skipped browser caches:', error);\n            }\n        }\n        // Skip IndexedDB clearing in silent mode to avoid blocking\n        // It will be cleared on next manual clear if needed\n        console.log('✅ Silent cache clear completed');\n    } catch (error) {\n        console.warn('Silent cache clear encountered error:', error);\n    // Don't throw error in silent mode\n    }\n}\n// Clear all application cache and data (full version with user feedback)\nasync function clearApplicationCache() {\n    try {\n        console.log('🧹 Clearing application cache and data...');\n        // Clear localStorage (except authentication data)\n        const authKeys = [\n            'firebase:authUser',\n            'firebase:host'\n        ];\n        const preservedData = {};\n        // Preserve auth data\n        authKeys.forEach((key)=>{\n            const keys = Object.keys(localStorage).filter((k)=>k.includes(key));\n            keys.forEach((k)=>{\n                preservedData[k] = localStorage.getItem(k);\n            });\n        });\n        // Clear all localStorage\n        localStorage.clear();\n        // Restore auth data\n        Object.entries(preservedData).forEach((param)=>{\n            let [key, value] = param;\n            if (value) {\n                localStorage.setItem(key, value);\n            }\n        });\n        // Clear sessionStorage\n        sessionStorage.clear();\n        console.log('🗑️ Storage cleared');\n        // Clear browser cache if supported\n        if ('caches' in window) {\n            try {\n                const cacheNames = await Promise.race([\n                    caches.keys(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache keys timeout')), 5000))\n                ]);\n                await Promise.race([\n                    Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName))),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache deletion timeout')), 5000))\n                ]);\n                console.log('🗑️ Browser caches cleared');\n            } catch (error) {\n                console.warn('Could not clear browser caches:', error);\n            }\n        }\n        // Clear IndexedDB with timeout protection\n        if ('indexedDB' in window) {\n            try {\n                // Use a more conservative approach for IndexedDB\n                const dbNames = [\n                    'firebaseLocalStorageDb',\n                    'firebase-heartbeat-database',\n                    'firebase-installations-database'\n                ];\n                for (const dbName of dbNames){\n                    try {\n                        await Promise.race([\n                            new Promise((resolve)=>{\n                                const deleteReq = indexedDB.deleteDatabase(dbName);\n                                deleteReq.onsuccess = ()=>resolve();\n                                deleteReq.onerror = ()=>resolve() // Don't fail if DB doesn't exist\n                                ;\n                                deleteReq.onblocked = ()=>resolve() // Continue even if blocked\n                                ;\n                                // Timeout after 3 seconds\n                                setTimeout(()=>resolve(), 3000);\n                            }),\n                            new Promise((_, reject)=>setTimeout(()=>reject(new Error('IndexedDB timeout')), 5000))\n                        ]);\n                    } catch (error) {\n                        console.warn(\"Could not clear IndexedDB \".concat(dbName, \":\"), error);\n                    }\n                }\n                console.log('🗑️ IndexedDB cleared');\n            } catch (error) {\n                console.warn('Could not clear IndexedDB:', error);\n            }\n        }\n        console.log('✅ Application cache cleared successfully');\n    } catch (error) {\n        console.error('Error clearing cache:', error);\n        throw error;\n    }\n}\n// Update server version (admin function)\nasync function updateServerVersion(newVersion) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, VERSION_DOC_PATH), {\n            version: newVersion,\n            updatedAt: new Date().toISOString(),\n            updatedBy: 'admin'\n        });\n        console.log(\"✅ Server version updated to \".concat(newVersion));\n    } catch (error) {\n        console.error('Error updating server version:', error);\n        throw error;\n    }\n}\n// Simple cache clear (fallback function)\nfunction simpleCacheClear() {\n    try {\n        // Clear storage\n        localStorage.clear();\n        sessionStorage.clear();\n        // Clear cookies\n        document.cookie.split(\";\").forEach((cookie)=>{\n            const eqPos = cookie.indexOf(\"=\");\n            const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();\n            if (name) {\n                document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/\");\n            }\n        });\n        console.log('✅ Simple cache clear completed');\n    } catch (error) {\n        console.error('Error in simple cache clear:', error);\n    }\n}\n// Get current version info\nfunction getVersionInfo() {\n    return {\n        currentVersion: CURRENT_VERSION,\n        localVersion: localStorage.getItem(LOCAL_VERSION_KEY),\n        lastCacheClear: localStorage.getItem(LAST_CACHE_CLEAR_KEY)\n    };\n}\n// Manual cache clear function for users\nasync function manualCacheClear() {\n    try {\n        // Add timeout to the entire operation\n        await Promise.race([\n            clearApplicationCache(),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache clear operation timed out')), 15000))\n        ]);\n        localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString());\n        // Show success message and reload\n        alert('Cache cleared successfully! The page will now reload to apply changes.');\n        window.location.reload();\n    } catch (error) {\n        console.error('Error during manual cache clear:', error);\n        // Try a simple reload as fallback\n        const fallback = confirm('Cache clearing encountered an issue. Would you like to try a simple page refresh instead?');\n        if (fallback) {\n            window.location.reload();\n        } else {\n            alert('Please try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)');\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdmVyc2lvblNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsK0VBQStFO0FBQ3ZCO0FBQ3pCO0FBRS9CLDREQUE0RDtBQUM1RCxNQUFNSSxrQkFBa0I7QUFFeEIsZ0NBQWdDO0FBQ2hDLE1BQU1DLG1CQUFtQjtBQUV6QixxQkFBcUI7QUFDckIsTUFBTUMsb0JBQW9CO0FBQzFCLE1BQU1DLHVCQUF1QjtBQUU3QixzRUFBc0U7QUFDL0QsZUFBZUM7SUFDcEIsSUFBSTtRQUNGQyxRQUFRQyxHQUFHLENBQUM7UUFFWiwyQkFBMkI7UUFDM0IsTUFBTUMsZUFBZUMsYUFBYUMsT0FBTyxDQUFDUDtRQUUxQyxvQ0FBb0M7UUFDcEMsSUFBSVEsZ0JBQWdCVjtRQUNwQixJQUFJO1lBQ0YsTUFBTVcsYUFBYSxNQUFNZCwwREFBTUEsQ0FBQ0QsdURBQUdBLENBQUNHLHlDQUFFQSxFQUFFRTtZQUN4QyxJQUFJVSxXQUFXQyxNQUFNLElBQUk7b0JBQ1BEO2dCQUFoQkQsZ0JBQWdCQyxFQUFBQSxtQkFBQUEsV0FBV0UsSUFBSSxnQkFBZkYsdUNBQUFBLGlCQUFtQkcsT0FBTyxLQUFJZDtZQUNoRDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkVixRQUFRVyxJQUFJLENBQUMsMERBQTBERDtRQUN6RTtRQUVBLCtCQUErQjtRQUMvQixNQUFNRSxpQkFBaUIsQ0FBQ1YsZ0JBQWdCQSxpQkFBaUJHO1FBRXpELElBQUlPLGdCQUFnQjtZQUNsQlosUUFBUUMsR0FBRyxDQUFDLGlDQUFtREksT0FBNUJILGdCQUFnQixRQUFPLE9BQW1CLE9BQWRHLGVBQWM7WUFFN0UscUNBQXFDO1lBQ3JDLElBQUk7Z0JBQ0YsTUFBTVE7Z0JBRU4sdUJBQXVCO2dCQUN2QlYsYUFBYVcsT0FBTyxDQUFDakIsbUJBQW1CUTtnQkFDeENGLGFBQWFXLE9BQU8sQ0FBQ2hCLHNCQUFzQixJQUFJaUIsT0FBT0MsV0FBVztnQkFFakVoQixRQUFRQyxHQUFHLENBQUM7Z0JBRVosZ0VBQWdFO2dCQUNoRWdCLFdBQVc7b0JBQ1RDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTtnQkFDeEIsR0FBRztnQkFFSCxPQUFPO1lBQ1QsRUFBRSxPQUFPVixPQUFPO2dCQUNkVixRQUFRVSxLQUFLLENBQUMsbURBQW1EQTtnQkFDakUsc0RBQXNEO2dCQUN0RFAsYUFBYVcsT0FBTyxDQUFDakIsbUJBQW1CUTtnQkFDeEMsT0FBTztZQUNUO1FBQ0Y7UUFFQUwsUUFBUUMsR0FBRyxDQUFDO1FBQ1osT0FBTztJQUNULEVBQUUsT0FBT1MsT0FBTztRQUNkVixRQUFRVSxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLGtFQUFrRTtBQUMzRCxlQUFlRztJQUNwQixJQUFJO1FBQ0ZiLFFBQVFDLEdBQUcsQ0FBQztRQUVaLDBFQUEwRTtRQUMxRSxNQUFNb0IsV0FBVztZQUFDO1lBQXFCO1NBQWdCO1FBQ3ZELE1BQU1DLGdCQUFrRCxDQUFDO1FBRXpELHFCQUFxQjtRQUNyQkQsU0FBU0UsT0FBTyxDQUFDQyxDQUFBQTtZQUNmLE1BQU1DLE9BQU9DLE9BQU9ELElBQUksQ0FBQ3RCLGNBQWN3QixNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFFBQVEsQ0FBQ0w7WUFDOURDLEtBQUtGLE9BQU8sQ0FBQ0ssQ0FBQUE7Z0JBQ1hOLGFBQWEsQ0FBQ00sRUFBRSxHQUFHekIsYUFBYUMsT0FBTyxDQUFDd0I7WUFDMUM7UUFDRjtRQUVBLHlCQUF5QjtRQUN6QnpCLGFBQWEyQixLQUFLO1FBRWxCLG9CQUFvQjtRQUNwQkosT0FBT0ssT0FBTyxDQUFDVCxlQUFlQyxPQUFPLENBQUM7Z0JBQUMsQ0FBQ0MsS0FBS1EsTUFBTTtZQUNqRCxJQUFJQSxPQUFPO2dCQUNUN0IsYUFBYVcsT0FBTyxDQUFDVSxLQUFLUTtZQUM1QjtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCQyxlQUFlSCxLQUFLO1FBRXBCLHVEQUF1RDtRQUN2RCxJQUFJLFlBQVlaLFFBQVE7WUFDdEIsSUFBSTtnQkFDRixNQUFNZ0IsYUFBYSxNQUFNQyxRQUFRQyxJQUFJLENBQUM7b0JBQ3BDQyxPQUFPWixJQUFJO29CQUNYLElBQUlVLFFBQWtCLENBQUNHLEdBQUdDLFNBQ3hCdEIsV0FBVyxJQUFNc0IsT0FBTyxJQUFJQyxNQUFNLHdCQUF3QjtpQkFFN0Q7Z0JBRUQsTUFBTUwsUUFBUUMsSUFBSSxDQUFDO29CQUNqQkQsUUFBUU0sR0FBRyxDQUFDUCxXQUFXUSxHQUFHLENBQUNDLENBQUFBLFlBQWFOLE9BQU9PLE1BQU0sQ0FBQ0Q7b0JBQ3RELElBQUlSLFFBQWMsQ0FBQ0csR0FBR0MsU0FDcEJ0QixXQUFXLElBQU1zQixPQUFPLElBQUlDLE1BQU0sNEJBQTRCO2lCQUVqRTtZQUNILEVBQUUsT0FBTzlCLE9BQU87Z0JBQ2Qsd0NBQXdDO2dCQUN4Q1YsUUFBUVcsSUFBSSxDQUFDLDhDQUE4Q0Q7WUFDN0Q7UUFDRjtRQUVBLDJEQUEyRDtRQUMzRCxvREFBb0Q7UUFFcERWLFFBQVFDLEdBQUcsQ0FBQztJQUNkLEVBQUUsT0FBT1MsT0FBTztRQUNkVixRQUFRVyxJQUFJLENBQUMseUNBQXlDRDtJQUN0RCxtQ0FBbUM7SUFDckM7QUFDRjtBQUVBLHlFQUF5RTtBQUNsRSxlQUFlbUM7SUFDcEIsSUFBSTtRQUNGN0MsUUFBUUMsR0FBRyxDQUFDO1FBRVosa0RBQWtEO1FBQ2xELE1BQU1vQixXQUFXO1lBQUM7WUFBcUI7U0FBZ0I7UUFDdkQsTUFBTUMsZ0JBQWtELENBQUM7UUFFekQscUJBQXFCO1FBQ3JCRCxTQUFTRSxPQUFPLENBQUNDLENBQUFBO1lBQ2YsTUFBTUMsT0FBT0MsT0FBT0QsSUFBSSxDQUFDdEIsY0FBY3dCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxDQUFDTDtZQUM5REMsS0FBS0YsT0FBTyxDQUFDSyxDQUFBQTtnQkFDWE4sYUFBYSxDQUFDTSxFQUFFLEdBQUd6QixhQUFhQyxPQUFPLENBQUN3QjtZQUMxQztRQUNGO1FBRUEseUJBQXlCO1FBQ3pCekIsYUFBYTJCLEtBQUs7UUFFbEIsb0JBQW9CO1FBQ3BCSixPQUFPSyxPQUFPLENBQUNULGVBQWVDLE9BQU8sQ0FBQztnQkFBQyxDQUFDQyxLQUFLUSxNQUFNO1lBQ2pELElBQUlBLE9BQU87Z0JBQ1Q3QixhQUFhVyxPQUFPLENBQUNVLEtBQUtRO1lBQzVCO1FBQ0Y7UUFFQSx1QkFBdUI7UUFDdkJDLGVBQWVILEtBQUs7UUFDcEI5QixRQUFRQyxHQUFHLENBQUM7UUFFWixtQ0FBbUM7UUFDbkMsSUFBSSxZQUFZaUIsUUFBUTtZQUN0QixJQUFJO2dCQUNGLE1BQU1nQixhQUFhLE1BQU1DLFFBQVFDLElBQUksQ0FBQztvQkFDcENDLE9BQU9aLElBQUk7b0JBQ1gsSUFBSVUsUUFBa0IsQ0FBQ0csR0FBR0MsU0FDeEJ0QixXQUFXLElBQU1zQixPQUFPLElBQUlDLE1BQU0sd0JBQXdCO2lCQUU3RDtnQkFFRCxNQUFNTCxRQUFRQyxJQUFJLENBQUM7b0JBQ2pCRCxRQUFRTSxHQUFHLENBQUNQLFdBQVdRLEdBQUcsQ0FBQ0MsQ0FBQUEsWUFBYU4sT0FBT08sTUFBTSxDQUFDRDtvQkFDdEQsSUFBSVIsUUFBYyxDQUFDRyxHQUFHQyxTQUNwQnRCLFdBQVcsSUFBTXNCLE9BQU8sSUFBSUMsTUFBTSw0QkFBNEI7aUJBRWpFO2dCQUNEeEMsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPUyxPQUFPO2dCQUNkVixRQUFRVyxJQUFJLENBQUMsbUNBQW1DRDtZQUNsRDtRQUNGO1FBRUEsMENBQTBDO1FBQzFDLElBQUksZUFBZVEsUUFBUTtZQUN6QixJQUFJO2dCQUNGLGlEQUFpRDtnQkFDakQsTUFBTTRCLFVBQVU7b0JBQUM7b0JBQTBCO29CQUErQjtpQkFBa0M7Z0JBRTVHLEtBQUssTUFBTUMsVUFBVUQsUUFBUztvQkFDNUIsSUFBSTt3QkFDRixNQUFNWCxRQUFRQyxJQUFJLENBQUM7NEJBQ2pCLElBQUlELFFBQWMsQ0FBQ2E7Z0NBQ2pCLE1BQU1DLFlBQVlDLFVBQVVDLGNBQWMsQ0FBQ0o7Z0NBQzNDRSxVQUFVRyxTQUFTLEdBQUcsSUFBTUo7Z0NBQzVCQyxVQUFVSSxPQUFPLEdBQUcsSUFBTUwsVUFBVSxpQ0FBaUM7O2dDQUNyRUMsVUFBVUssU0FBUyxHQUFHLElBQU1OLFVBQVUsMkJBQTJCOztnQ0FFakUsMEJBQTBCO2dDQUMxQi9CLFdBQVcsSUFBTStCLFdBQVc7NEJBQzlCOzRCQUNBLElBQUliLFFBQWMsQ0FBQ0csR0FBR0MsU0FDcEJ0QixXQUFXLElBQU1zQixPQUFPLElBQUlDLE1BQU0sdUJBQXVCO3lCQUU1RDtvQkFDSCxFQUFFLE9BQU85QixPQUFPO3dCQUNkVixRQUFRVyxJQUFJLENBQUMsNkJBQW9DLE9BQVBvQyxRQUFPLE1BQUlyQztvQkFDdkQ7Z0JBQ0Y7Z0JBQ0FWLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT1MsT0FBTztnQkFDZFYsUUFBUVcsSUFBSSxDQUFDLDhCQUE4QkQ7WUFDN0M7UUFDRjtRQUVBVixRQUFRQyxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9TLE9BQU87UUFDZFYsUUFBUVUsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsTUFBTUE7SUFDUjtBQUNGO0FBRUEseUNBQXlDO0FBQ2xDLGVBQWU2QyxvQkFBb0JDLFVBQWtCO0lBQzFELElBQUk7UUFDRixNQUFNL0QsMERBQU1BLENBQUNGLHVEQUFHQSxDQUFDRyx5Q0FBRUEsRUFBRUUsbUJBQW1CO1lBQ3RDYSxTQUFTK0M7WUFDVEMsV0FBVyxJQUFJMUMsT0FBT0MsV0FBVztZQUNqQzBDLFdBQVc7UUFDYjtRQUVBMUQsUUFBUUMsR0FBRyxDQUFDLCtCQUEwQyxPQUFYdUQ7SUFDN0MsRUFBRSxPQUFPOUMsT0FBTztRQUNkVixRQUFRVSxLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSx5Q0FBeUM7QUFDbEMsU0FBU2lEO0lBQ2QsSUFBSTtRQUNGLGdCQUFnQjtRQUNoQnhELGFBQWEyQixLQUFLO1FBQ2xCRyxlQUFlSCxLQUFLO1FBRXBCLGdCQUFnQjtRQUNoQjhCLFNBQVNDLE1BQU0sQ0FBQ0MsS0FBSyxDQUFDLEtBQUt2QyxPQUFPLENBQUNzQyxDQUFBQTtZQUNqQyxNQUFNRSxRQUFRRixPQUFPRyxPQUFPLENBQUM7WUFDN0IsTUFBTUMsT0FBT0YsUUFBUSxDQUFDLElBQUlGLE9BQU9LLFNBQVMsQ0FBQyxHQUFHSCxPQUFPSSxJQUFJLEtBQUtOLE9BQU9NLElBQUk7WUFDekUsSUFBSUYsTUFBTTtnQkFDUkwsU0FBU0MsTUFBTSxHQUFHLEdBQVEsT0FBTEksTUFBSztZQUM1QjtRQUNGO1FBRUFqRSxRQUFRQyxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9TLE9BQU87UUFDZFYsUUFBUVUsS0FBSyxDQUFDLGdDQUFnQ0E7SUFDaEQ7QUFDRjtBQUVBLDJCQUEyQjtBQUNwQixTQUFTMEQ7SUFDZCxPQUFPO1FBQ0xDLGdCQUFnQjFFO1FBQ2hCTyxjQUFjQyxhQUFhQyxPQUFPLENBQUNQO1FBQ25DeUUsZ0JBQWdCbkUsYUFBYUMsT0FBTyxDQUFDTjtJQUN2QztBQUNGO0FBRUEsd0NBQXdDO0FBQ2pDLGVBQWV5RTtJQUNwQixJQUFJO1FBQ0Ysc0NBQXNDO1FBQ3RDLE1BQU1wQyxRQUFRQyxJQUFJLENBQUM7WUFDakJTO1lBQ0EsSUFBSVYsUUFBYyxDQUFDRyxHQUFHQyxTQUNwQnRCLFdBQVcsSUFBTXNCLE9BQU8sSUFBSUMsTUFBTSxxQ0FBcUM7U0FFMUU7UUFFRHJDLGFBQWFXLE9BQU8sQ0FBQ2hCLHNCQUFzQixJQUFJaUIsT0FBT0MsV0FBVztRQUVqRSxrQ0FBa0M7UUFDbEN3RCxNQUFNO1FBQ050RCxPQUFPQyxRQUFRLENBQUNDLE1BQU07SUFDeEIsRUFBRSxPQUFPVixPQUFPO1FBQ2RWLFFBQVFVLEtBQUssQ0FBQyxvQ0FBb0NBO1FBRWxELGtDQUFrQztRQUNsQyxNQUFNK0QsV0FBV0MsUUFDZjtRQUdGLElBQUlELFVBQVU7WUFDWnZELE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTtRQUN4QixPQUFPO1lBQ0xvRCxNQUFNO1FBQ1I7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcbGliXFx2ZXJzaW9uU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBWZXJzaW9uIG1hbmFnZW1lbnQgc2VydmljZSB0byBoYW5kbGUgYXV0b21hdGljIGNhY2hlIGNsZWFyaW5nIG9uIGRlcGxveW1lbnRzXG5pbXBvcnQgeyBkb2MsIGdldERvYywgc2V0RG9jIH0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJ1xuaW1wb3J0IHsgZGIgfSBmcm9tICcuL2ZpcmViYXNlJ1xuXG4vLyBDdXJyZW50IGFwcCB2ZXJzaW9uIC0gaW5jcmVtZW50IHRoaXMgd2l0aCBlYWNoIGRlcGxveW1lbnRcbmNvbnN0IENVUlJFTlRfVkVSU0lPTiA9ICcxLjEuMCdcblxuLy8gVmVyc2lvbiB0cmFja2luZyBpbiBGaXJlc3RvcmVcbmNvbnN0IFZFUlNJT05fRE9DX1BBVEggPSAnc3lzdGVtL3ZlcnNpb24nXG5cbi8vIExvY2FsIHN0b3JhZ2Uga2V5c1xuY29uc3QgTE9DQUxfVkVSU0lPTl9LRVkgPSAnbXl0dWJlX2FwcF92ZXJzaW9uJ1xuY29uc3QgTEFTVF9DQUNIRV9DTEVBUl9LRVkgPSAnbXl0dWJlX2xhc3RfY2FjaGVfY2xlYXInXG5cbi8vIENoZWNrIGlmIGFwcCB2ZXJzaW9uIGhhcyBjaGFuZ2VkIGFuZCBjbGVhciBjYWNoZSBpZiBuZWVkZWQgKFNJTEVOVClcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjaGVja1ZlcnNpb25BbmRDbGVhckNhY2hlKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIFNpbGVudGx5IGNoZWNraW5nIGFwcCB2ZXJzaW9uIGZvciBjYWNoZSBtYW5hZ2VtZW50Li4uJylcblxuICAgIC8vIEdldCBzdG9yZWQgbG9jYWwgdmVyc2lvblxuICAgIGNvbnN0IGxvY2FsVmVyc2lvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKExPQ0FMX1ZFUlNJT05fS0VZKVxuXG4gICAgLy8gR2V0IHNlcnZlciB2ZXJzaW9uIGZyb20gRmlyZXN0b3JlXG4gICAgbGV0IHNlcnZlclZlcnNpb24gPSBDVVJSRU5UX1ZFUlNJT05cbiAgICB0cnkge1xuICAgICAgY29uc3QgdmVyc2lvbkRvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsIFZFUlNJT05fRE9DX1BBVEgpKVxuICAgICAgaWYgKHZlcnNpb25Eb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgc2VydmVyVmVyc2lvbiA9IHZlcnNpb25Eb2MuZGF0YSgpPy52ZXJzaW9uIHx8IENVUlJFTlRfVkVSU0lPTlxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0NvdWxkIG5vdCBmZXRjaCBzZXJ2ZXIgdmVyc2lvbiwgdXNpbmcgY3VycmVudCB2ZXJzaW9uOicsIGVycm9yKVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHZlcnNpb24gaGFzIGNoYW5nZWRcbiAgICBjb25zdCB2ZXJzaW9uQ2hhbmdlZCA9ICFsb2NhbFZlcnNpb24gfHwgbG9jYWxWZXJzaW9uICE9PSBzZXJ2ZXJWZXJzaW9uXG5cbiAgICBpZiAodmVyc2lvbkNoYW5nZWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFZlcnNpb24gY2hhbmdlZDogJHtsb2NhbFZlcnNpb24gfHwgJ25vbmUnfSDihpIgJHtzZXJ2ZXJWZXJzaW9ufSAtIENsZWFyaW5nIGNhY2hlIHNpbGVudGx5Li4uYClcblxuICAgICAgLy8gQ2xlYXIgY2FjaGUgc2lsZW50bHkgaW4gYmFja2dyb3VuZFxuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgY2xlYXJBcHBsaWNhdGlvbkNhY2hlU2lsZW50bHkoKVxuXG4gICAgICAgIC8vIFVwZGF0ZSBsb2NhbCB2ZXJzaW9uXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKExPQ0FMX1ZFUlNJT05fS0VZLCBzZXJ2ZXJWZXJzaW9uKVxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShMQVNUX0NBQ0hFX0NMRUFSX0tFWSwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpKVxuXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgQ2FjaGUgY2xlYXJlZCBzaWxlbnRseSBkdWUgdG8gdmVyc2lvbiB1cGRhdGUnKVxuXG4gICAgICAgIC8vIFNpbGVudGx5IHJlbG9hZCB0aGUgcGFnZSBhZnRlciBhIHNob3J0IGRlbGF5IHRvIGFwcGx5IGNoYW5nZXNcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpXG4gICAgICAgIH0sIDEwMDApXG5cbiAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1NpbGVudCBjYWNoZSBjbGVhciBmYWlsZWQsIGNvbnRpbnVpbmcgbm9ybWFsbHk6JywgZXJyb3IpXG4gICAgICAgIC8vIERvbid0IGRpc3J1cHQgdXNlciBleHBlcmllbmNlIGlmIHNpbGVudCBjbGVhciBmYWlsc1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShMT0NBTF9WRVJTSU9OX0tFWSwgc2VydmVyVmVyc2lvbilcbiAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ+KchSBWZXJzaW9uIHVuY2hhbmdlZCwgbm8gY2FjaGUgY2xlYXJpbmcgbmVlZGVkJylcbiAgICByZXR1cm4gZmFsc2VcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyB2ZXJzaW9uOicsIGVycm9yKVxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8vIFNpbGVudCBjYWNoZSBjbGVhcmluZyAobm8gdXNlciBpbnRlcmFjdGlvbiwgZmFzdGVyLCBiYWNrZ3JvdW5kKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNsZWFyQXBwbGljYXRpb25DYWNoZVNpbGVudGx5KCk6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCfwn5SHIFNpbGVudGx5IGNsZWFyaW5nIGFwcGxpY2F0aW9uIGNhY2hlLi4uJylcblxuICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZSAoZXhjZXB0IGF1dGhlbnRpY2F0aW9uIGRhdGEpIC0gcHJlc2VydmUgdXNlciBzZXNzaW9uXG4gICAgY29uc3QgYXV0aEtleXMgPSBbJ2ZpcmViYXNlOmF1dGhVc2VyJywgJ2ZpcmViYXNlOmhvc3QnXVxuICAgIGNvbnN0IHByZXNlcnZlZERhdGE6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIHwgbnVsbCB9ID0ge31cblxuICAgIC8vIFByZXNlcnZlIGF1dGggZGF0YVxuICAgIGF1dGhLZXlzLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpLmZpbHRlcihrID0+IGsuaW5jbHVkZXMoa2V5KSlcbiAgICAgIGtleXMuZm9yRWFjaChrID0+IHtcbiAgICAgICAgcHJlc2VydmVkRGF0YVtrXSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGspXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyBDbGVhciBhbGwgbG9jYWxTdG9yYWdlXG4gICAgbG9jYWxTdG9yYWdlLmNsZWFyKClcblxuICAgIC8vIFJlc3RvcmUgYXV0aCBkYXRhXG4gICAgT2JqZWN0LmVudHJpZXMocHJlc2VydmVkRGF0YSkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSlcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gQ2xlYXIgc2Vzc2lvblN0b3JhZ2VcbiAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpXG5cbiAgICAvLyBDbGVhciBicm93c2VyIGNhY2hlIHNpbGVudGx5ICh3aXRoIHNob3J0ZXIgdGltZW91dHMpXG4gICAgaWYgKCdjYWNoZXMnIGluIHdpbmRvdykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY2FjaGVOYW1lcyA9IGF3YWl0IFByb21pc2UucmFjZShbXG4gICAgICAgICAgY2FjaGVzLmtleXMoKSxcbiAgICAgICAgICBuZXcgUHJvbWlzZTxzdHJpbmdbXT4oKF8sIHJlamVjdCkgPT5cbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignQ2FjaGUga2V5cyB0aW1lb3V0JykpLCAyMDAwKVxuICAgICAgICAgIClcbiAgICAgICAgXSlcblxuICAgICAgICBhd2FpdCBQcm9taXNlLnJhY2UoW1xuICAgICAgICAgIFByb21pc2UuYWxsKGNhY2hlTmFtZXMubWFwKGNhY2hlTmFtZSA9PiBjYWNoZXMuZGVsZXRlKGNhY2hlTmFtZSkpKSxcbiAgICAgICAgICBuZXcgUHJvbWlzZTx2b2lkPigoXywgcmVqZWN0KSA9PlxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiByZWplY3QobmV3IEVycm9yKCdDYWNoZSBkZWxldGlvbiB0aW1lb3V0JykpLCAyMDAwKVxuICAgICAgICAgIClcbiAgICAgICAgXSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIFNpbGVudGx5IGlnbm9yZSBjYWNoZSBjbGVhcmluZyBlcnJvcnNcbiAgICAgICAgY29uc29sZS53YXJuKCdTaWxlbnQgY2FjaGUgY2xlYXIgc2tpcHBlZCBicm93c2VyIGNhY2hlczonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTa2lwIEluZGV4ZWREQiBjbGVhcmluZyBpbiBzaWxlbnQgbW9kZSB0byBhdm9pZCBibG9ja2luZ1xuICAgIC8vIEl0IHdpbGwgYmUgY2xlYXJlZCBvbiBuZXh0IG1hbnVhbCBjbGVhciBpZiBuZWVkZWRcblxuICAgIGNvbnNvbGUubG9nKCfinIUgU2lsZW50IGNhY2hlIGNsZWFyIGNvbXBsZXRlZCcpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS53YXJuKCdTaWxlbnQgY2FjaGUgY2xlYXIgZW5jb3VudGVyZWQgZXJyb3I6JywgZXJyb3IpXG4gICAgLy8gRG9uJ3QgdGhyb3cgZXJyb3IgaW4gc2lsZW50IG1vZGVcbiAgfVxufVxuXG4vLyBDbGVhciBhbGwgYXBwbGljYXRpb24gY2FjaGUgYW5kIGRhdGEgKGZ1bGwgdmVyc2lvbiB3aXRoIHVzZXIgZmVlZGJhY2spXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2xlYXJBcHBsaWNhdGlvbkNhY2hlKCk6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCfwn6e5IENsZWFyaW5nIGFwcGxpY2F0aW9uIGNhY2hlIGFuZCBkYXRhLi4uJylcblxuICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZSAoZXhjZXB0IGF1dGhlbnRpY2F0aW9uIGRhdGEpXG4gICAgY29uc3QgYXV0aEtleXMgPSBbJ2ZpcmViYXNlOmF1dGhVc2VyJywgJ2ZpcmViYXNlOmhvc3QnXVxuICAgIGNvbnN0IHByZXNlcnZlZERhdGE6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIHwgbnVsbCB9ID0ge31cblxuICAgIC8vIFByZXNlcnZlIGF1dGggZGF0YVxuICAgIGF1dGhLZXlzLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpLmZpbHRlcihrID0+IGsuaW5jbHVkZXMoa2V5KSlcbiAgICAgIGtleXMuZm9yRWFjaChrID0+IHtcbiAgICAgICAgcHJlc2VydmVkRGF0YVtrXSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGspXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyBDbGVhciBhbGwgbG9jYWxTdG9yYWdlXG4gICAgbG9jYWxTdG9yYWdlLmNsZWFyKClcblxuICAgIC8vIFJlc3RvcmUgYXV0aCBkYXRhXG4gICAgT2JqZWN0LmVudHJpZXMocHJlc2VydmVkRGF0YSkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSlcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gQ2xlYXIgc2Vzc2lvblN0b3JhZ2VcbiAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpXG4gICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gU3RvcmFnZSBjbGVhcmVkJylcblxuICAgIC8vIENsZWFyIGJyb3dzZXIgY2FjaGUgaWYgc3VwcG9ydGVkXG4gICAgaWYgKCdjYWNoZXMnIGluIHdpbmRvdykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY2FjaGVOYW1lcyA9IGF3YWl0IFByb21pc2UucmFjZShbXG4gICAgICAgICAgY2FjaGVzLmtleXMoKSxcbiAgICAgICAgICBuZXcgUHJvbWlzZTxzdHJpbmdbXT4oKF8sIHJlamVjdCkgPT5cbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignQ2FjaGUga2V5cyB0aW1lb3V0JykpLCA1MDAwKVxuICAgICAgICAgIClcbiAgICAgICAgXSlcblxuICAgICAgICBhd2FpdCBQcm9taXNlLnJhY2UoW1xuICAgICAgICAgIFByb21pc2UuYWxsKGNhY2hlTmFtZXMubWFwKGNhY2hlTmFtZSA9PiBjYWNoZXMuZGVsZXRlKGNhY2hlTmFtZSkpKSxcbiAgICAgICAgICBuZXcgUHJvbWlzZTx2b2lkPigoXywgcmVqZWN0KSA9PlxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiByZWplY3QobmV3IEVycm9yKCdDYWNoZSBkZWxldGlvbiB0aW1lb3V0JykpLCA1MDAwKVxuICAgICAgICAgIClcbiAgICAgICAgXSlcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gQnJvd3NlciBjYWNoZXMgY2xlYXJlZCcpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0NvdWxkIG5vdCBjbGVhciBicm93c2VyIGNhY2hlczonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDbGVhciBJbmRleGVkREIgd2l0aCB0aW1lb3V0IHByb3RlY3Rpb25cbiAgICBpZiAoJ2luZGV4ZWREQicgaW4gd2luZG93KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBVc2UgYSBtb3JlIGNvbnNlcnZhdGl2ZSBhcHByb2FjaCBmb3IgSW5kZXhlZERCXG4gICAgICAgIGNvbnN0IGRiTmFtZXMgPSBbJ2ZpcmViYXNlTG9jYWxTdG9yYWdlRGInLCAnZmlyZWJhc2UtaGVhcnRiZWF0LWRhdGFiYXNlJywgJ2ZpcmViYXNlLWluc3RhbGxhdGlvbnMtZGF0YWJhc2UnXVxuXG4gICAgICAgIGZvciAoY29uc3QgZGJOYW1lIG9mIGRiTmFtZXMpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgUHJvbWlzZS5yYWNlKFtcbiAgICAgICAgICAgICAgbmV3IFByb21pc2U8dm9pZD4oKHJlc29sdmUpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkZWxldGVSZXEgPSBpbmRleGVkREIuZGVsZXRlRGF0YWJhc2UoZGJOYW1lKVxuICAgICAgICAgICAgICAgIGRlbGV0ZVJlcS5vbnN1Y2Nlc3MgPSAoKSA9PiByZXNvbHZlKClcbiAgICAgICAgICAgICAgICBkZWxldGVSZXEub25lcnJvciA9ICgpID0+IHJlc29sdmUoKSAvLyBEb24ndCBmYWlsIGlmIERCIGRvZXNuJ3QgZXhpc3RcbiAgICAgICAgICAgICAgICBkZWxldGVSZXEub25ibG9ja2VkID0gKCkgPT4gcmVzb2x2ZSgpIC8vIENvbnRpbnVlIGV2ZW4gaWYgYmxvY2tlZFxuXG4gICAgICAgICAgICAgICAgLy8gVGltZW91dCBhZnRlciAzIHNlY29uZHNcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHJlc29sdmUoKSwgMzAwMClcbiAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgIG5ldyBQcm9taXNlPHZvaWQ+KChfLCByZWplY3QpID0+XG4gICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiByZWplY3QobmV3IEVycm9yKCdJbmRleGVkREIgdGltZW91dCcpKSwgNTAwMClcbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgXSlcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKGBDb3VsZCBub3QgY2xlYXIgSW5kZXhlZERCICR7ZGJOYW1lfTpgLCBlcnJvcilcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gSW5kZXhlZERCIGNsZWFyZWQnKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdDb3VsZCBub3QgY2xlYXIgSW5kZXhlZERCOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfinIUgQXBwbGljYXRpb24gY2FjaGUgY2xlYXJlZCBzdWNjZXNzZnVsbHknKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsZWFyaW5nIGNhY2hlOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gVXBkYXRlIHNlcnZlciB2ZXJzaW9uIChhZG1pbiBmdW5jdGlvbilcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVTZXJ2ZXJWZXJzaW9uKG5ld1ZlcnNpb246IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIGF3YWl0IHNldERvYyhkb2MoZGIsIFZFUlNJT05fRE9DX1BBVEgpLCB7XG4gICAgICB2ZXJzaW9uOiBuZXdWZXJzaW9uLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkQnk6ICdhZG1pbidcbiAgICB9KVxuICAgIFxuICAgIGNvbnNvbGUubG9nKGDinIUgU2VydmVyIHZlcnNpb24gdXBkYXRlZCB0byAke25ld1ZlcnNpb259YClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBzZXJ2ZXIgdmVyc2lvbjonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFNpbXBsZSBjYWNoZSBjbGVhciAoZmFsbGJhY2sgZnVuY3Rpb24pXG5leHBvcnQgZnVuY3Rpb24gc2ltcGxlQ2FjaGVDbGVhcigpOiB2b2lkIHtcbiAgdHJ5IHtcbiAgICAvLyBDbGVhciBzdG9yYWdlXG4gICAgbG9jYWxTdG9yYWdlLmNsZWFyKClcbiAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpXG5cbiAgICAvLyBDbGVhciBjb29raWVzXG4gICAgZG9jdW1lbnQuY29va2llLnNwbGl0KFwiO1wiKS5mb3JFYWNoKGNvb2tpZSA9PiB7XG4gICAgICBjb25zdCBlcVBvcyA9IGNvb2tpZS5pbmRleE9mKFwiPVwiKVxuICAgICAgY29uc3QgbmFtZSA9IGVxUG9zID4gLTEgPyBjb29raWUuc3Vic3RyaW5nKDAsIGVxUG9zKS50cmltKCkgOiBjb29raWUudHJpbSgpXG4gICAgICBpZiAobmFtZSkge1xuICAgICAgICBkb2N1bWVudC5jb29raWUgPSBgJHtuYW1lfT07ZXhwaXJlcz1UaHUsIDAxIEphbiAxOTcwIDAwOjAwOjAwIEdNVDtwYXRoPS9gXG4gICAgICB9XG4gICAgfSlcblxuICAgIGNvbnNvbGUubG9nKCfinIUgU2ltcGxlIGNhY2hlIGNsZWFyIGNvbXBsZXRlZCcpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gc2ltcGxlIGNhY2hlIGNsZWFyOicsIGVycm9yKVxuICB9XG59XG5cbi8vIEdldCBjdXJyZW50IHZlcnNpb24gaW5mb1xuZXhwb3J0IGZ1bmN0aW9uIGdldFZlcnNpb25JbmZvKCkge1xuICByZXR1cm4ge1xuICAgIGN1cnJlbnRWZXJzaW9uOiBDVVJSRU5UX1ZFUlNJT04sXG4gICAgbG9jYWxWZXJzaW9uOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShMT0NBTF9WRVJTSU9OX0tFWSksXG4gICAgbGFzdENhY2hlQ2xlYXI6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKExBU1RfQ0FDSEVfQ0xFQVJfS0VZKVxuICB9XG59XG5cbi8vIE1hbnVhbCBjYWNoZSBjbGVhciBmdW5jdGlvbiBmb3IgdXNlcnNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBtYW51YWxDYWNoZUNsZWFyKCk6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIC8vIEFkZCB0aW1lb3V0IHRvIHRoZSBlbnRpcmUgb3BlcmF0aW9uXG4gICAgYXdhaXQgUHJvbWlzZS5yYWNlKFtcbiAgICAgIGNsZWFyQXBwbGljYXRpb25DYWNoZSgpLFxuICAgICAgbmV3IFByb21pc2U8dm9pZD4oKF8sIHJlamVjdCkgPT5cbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiByZWplY3QobmV3IEVycm9yKCdDYWNoZSBjbGVhciBvcGVyYXRpb24gdGltZWQgb3V0JykpLCAxNTAwMClcbiAgICAgIClcbiAgICBdKVxuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oTEFTVF9DQUNIRV9DTEVBUl9LRVksIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSlcblxuICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlIGFuZCByZWxvYWRcbiAgICBhbGVydCgnQ2FjaGUgY2xlYXJlZCBzdWNjZXNzZnVsbHkhIFRoZSBwYWdlIHdpbGwgbm93IHJlbG9hZCB0byBhcHBseSBjaGFuZ2VzLicpXG4gICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZHVyaW5nIG1hbnVhbCBjYWNoZSBjbGVhcjonLCBlcnJvcilcblxuICAgIC8vIFRyeSBhIHNpbXBsZSByZWxvYWQgYXMgZmFsbGJhY2tcbiAgICBjb25zdCBmYWxsYmFjayA9IGNvbmZpcm0oXG4gICAgICAnQ2FjaGUgY2xlYXJpbmcgZW5jb3VudGVyZWQgYW4gaXNzdWUuIFdvdWxkIHlvdSBsaWtlIHRvIHRyeSBhIHNpbXBsZSBwYWdlIHJlZnJlc2ggaW5zdGVhZD8nXG4gICAgKVxuXG4gICAgaWYgKGZhbGxiYWNrKSB7XG4gICAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKClcbiAgICB9IGVsc2Uge1xuICAgICAgYWxlcnQoJ1BsZWFzZSB0cnkgcmVmcmVzaGluZyB0aGUgcGFnZSBtYW51YWxseSAoQ3RybCtGNSBvciBDbWQrU2hpZnQrUiknKVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImRvYyIsImdldERvYyIsInNldERvYyIsImRiIiwiQ1VSUkVOVF9WRVJTSU9OIiwiVkVSU0lPTl9ET0NfUEFUSCIsIkxPQ0FMX1ZFUlNJT05fS0VZIiwiTEFTVF9DQUNIRV9DTEVBUl9LRVkiLCJjaGVja1ZlcnNpb25BbmRDbGVhckNhY2hlIiwiY29uc29sZSIsImxvZyIsImxvY2FsVmVyc2lvbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzZXJ2ZXJWZXJzaW9uIiwidmVyc2lvbkRvYyIsImV4aXN0cyIsImRhdGEiLCJ2ZXJzaW9uIiwiZXJyb3IiLCJ3YXJuIiwidmVyc2lvbkNoYW5nZWQiLCJjbGVhckFwcGxpY2F0aW9uQ2FjaGVTaWxlbnRseSIsInNldEl0ZW0iLCJEYXRlIiwidG9JU09TdHJpbmciLCJzZXRUaW1lb3V0Iiwid2luZG93IiwibG9jYXRpb24iLCJyZWxvYWQiLCJhdXRoS2V5cyIsInByZXNlcnZlZERhdGEiLCJmb3JFYWNoIiwia2V5Iiwia2V5cyIsIk9iamVjdCIsImZpbHRlciIsImsiLCJpbmNsdWRlcyIsImNsZWFyIiwiZW50cmllcyIsInZhbHVlIiwic2Vzc2lvblN0b3JhZ2UiLCJjYWNoZU5hbWVzIiwiUHJvbWlzZSIsInJhY2UiLCJjYWNoZXMiLCJfIiwicmVqZWN0IiwiRXJyb3IiLCJhbGwiLCJtYXAiLCJjYWNoZU5hbWUiLCJkZWxldGUiLCJjbGVhckFwcGxpY2F0aW9uQ2FjaGUiLCJkYk5hbWVzIiwiZGJOYW1lIiwicmVzb2x2ZSIsImRlbGV0ZVJlcSIsImluZGV4ZWREQiIsImRlbGV0ZURhdGFiYXNlIiwib25zdWNjZXNzIiwib25lcnJvciIsIm9uYmxvY2tlZCIsInVwZGF0ZVNlcnZlclZlcnNpb24iLCJuZXdWZXJzaW9uIiwidXBkYXRlZEF0IiwidXBkYXRlZEJ5Iiwic2ltcGxlQ2FjaGVDbGVhciIsImRvY3VtZW50IiwiY29va2llIiwic3BsaXQiLCJlcVBvcyIsImluZGV4T2YiLCJuYW1lIiwic3Vic3RyaW5nIiwidHJpbSIsImdldFZlcnNpb25JbmZvIiwiY3VycmVudFZlcnNpb24iLCJsYXN0Q2FjaGVDbGVhciIsIm1hbnVhbENhY2hlQ2xlYXIiLCJhbGVydCIsImZhbGxiYWNrIiwiY29uZmlybSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/versionService.ts\n"));

/***/ })

}]);