"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_adminDataService_ts"],{

/***/ "(app-pages-browser)/./src/lib/adminDataService.ts":
/*!*************************************!*\
  !*** ./src/lib/adminDataService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAdminCache: () => (/* binding */ clearAdminCache),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getAdminDashboardStats: () => (/* binding */ getAdminDashboardStats),\n/* harmony export */   getAllPendingWithdrawals: () => (/* binding */ getAllPendingWithdrawals),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllWithdrawals: () => (/* binding */ getAllWithdrawals),\n/* harmony export */   getTotalUserCount: () => (/* binding */ getTotalUserCount),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   getWithdrawals: () => (/* binding */ getWithdrawals),\n/* harmony export */   searchUsers: () => (/* binding */ searchUsers),\n/* harmony export */   searchUsersByMobile: () => (/* binding */ searchUsersByMobile),\n/* harmony export */   updateUser: () => (/* binding */ updateUser),\n/* harmony export */   updateWithdrawalStatus: () => (/* binding */ updateWithdrawalStatus)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _dataService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n\n\n\n// Cache for admin data\nconst adminCache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\n// Helper function to check cache\nfunction getCachedData(key) {\n    const cached = adminCache.get(key);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.data;\n    }\n    return null;\n}\n// Helper function to set cache\nfunction setCachedData(key, data) {\n    adminCache.set(key, {\n        data,\n        timestamp: Date.now()\n    });\n}\n// Get dashboard statistics\nasync function getAdminDashboardStats() {\n    const cacheKey = 'dashboard-stats';\n    const cached = getCachedData(cacheKey);\n    if (cached) return cached;\n    try {\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayTimestamp = firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today);\n        // Get total users\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users));\n        const totalUsers = usersSnapshot.size;\n        // Get today's new users\n        const todayUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, '>=', todayTimestamp));\n        const todayUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(todayUsersQuery);\n        const todayUsers = todayUsersSnapshot.size;\n        // Calculate total videos and earnings from all users\n        let totalVideos = 0;\n        let totalEarnings = 0;\n        let todayVideos = 0;\n        let todayEarnings = 0;\n        usersSnapshot.forEach((doc)=>{\n            var _data_FIELD_NAMES_lastVideoDate;\n            const data = doc.data();\n            totalVideos += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.totalVideos] || 0;\n            totalEarnings += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.wallet] || 0;\n            // Check if last video was today\n            const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n            if (lastVideoDate && lastVideoDate.toDateString() === today.toDateString()) {\n                todayVideos += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.todayVideos] || 0;\n            }\n        });\n        // Get today's transactions for earnings - simplified to avoid index requirement\n        try {\n            const allTransactionsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.type, '==', 'video_earning'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1000));\n            const allTransactionsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allTransactionsQuery);\n            // Filter in memory for today's transactions\n            allTransactionsSnapshot.forEach((doc)=>{\n                var _data_FIELD_NAMES_date;\n                const data = doc.data();\n                const transactionDate = (_data_FIELD_NAMES_date = data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date]) === null || _data_FIELD_NAMES_date === void 0 ? void 0 : _data_FIELD_NAMES_date.toDate();\n                if (transactionDate && transactionDate >= today) {\n                    todayEarnings += data[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.amount] || 0;\n                }\n            });\n        } catch (transactionError) {\n            console.warn('Could not fetch today\\'s transactions:', transactionError);\n        // Continue without today's earnings data\n        }\n        // Get pending withdrawals\n        const pendingWithdrawalsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'));\n        const pendingWithdrawalsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(pendingWithdrawalsQuery);\n        const pendingWithdrawals = pendingWithdrawalsSnapshot.size;\n        // Get today's withdrawals\n        const todayWithdrawalsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('date', '>=', todayTimestamp));\n        const todayWithdrawalsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(todayWithdrawalsQuery);\n        const todayWithdrawalsCount = todayWithdrawalsSnapshot.size;\n        const stats = {\n            totalUsers,\n            totalVideos,\n            totalEarnings,\n            pendingWithdrawals,\n            todayUsers,\n            todayVideos,\n            todayEarnings,\n            todayWithdrawals: todayWithdrawalsCount\n        };\n        setCachedData(cacheKey, stats);\n        return stats;\n    } catch (error) {\n        console.error('Error getting admin dashboard stats:', error);\n        throw error;\n    }\n}\n// Get all users with pagination\nasync function getUsers() {\n    let pageSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, lastDoc = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const users = snapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate, _doc_data_FIELD_NAMES_planExpiry;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate(),\n                planExpiry: (_doc_data_FIELD_NAMES_planExpiry = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]) === null || _doc_data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _doc_data_FIELD_NAMES_planExpiry.toDate()\n            };\n        });\n        return {\n            users,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting users:', error);\n        throw error;\n    }\n}\n// Search users by mobile number (exact match)\nasync function searchUsersByMobile(mobile) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.mobile, '==', mobile));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate, _doc_data_FIELD_NAMES_planExpiry;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate(),\n                planExpiry: (_doc_data_FIELD_NAMES_planExpiry = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]) === null || _doc_data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _doc_data_FIELD_NAMES_planExpiry.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error searching users by mobile:', error);\n        throw error;\n    }\n}\n// Enhanced search function for users (searches across all users)\nasync function searchUsers(searchTerm) {\n    try {\n        if (!searchTerm || searchTerm.trim().length === 0) {\n            return [];\n        }\n        const searchTermLower = searchTerm.toLowerCase().trim();\n        // Get all users (we need to search across all users, not just paginated results)\n        // Note: For large datasets, consider implementing server-side search or using Algolia\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const allUsers = snapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate, _doc_data_FIELD_NAMES_planExpiry;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate(),\n                planExpiry: (_doc_data_FIELD_NAMES_planExpiry = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]) === null || _doc_data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _doc_data_FIELD_NAMES_planExpiry.toDate()\n            };\n        });\n        // Filter users based on search term (client-side filtering)\n        const filteredUsers = allUsers.filter((user)=>{\n            // Safely convert to string and then to lowercase\n            const name = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.name] || '').toLowerCase();\n            const email = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.email] || '').toLowerCase();\n            const mobile = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.mobile] || '').toLowerCase();\n            const referralCode = String(user[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.referralCode] || '').toLowerCase();\n            return name.includes(searchTermLower) || email.includes(searchTermLower) || mobile.includes(searchTermLower) || referralCode.includes(searchTermLower);\n        });\n        return filteredUsers;\n    } catch (error) {\n        console.error('Error searching users:', error);\n        throw error;\n    }\n}\n// Get all users without pagination (for search and export)\nasync function getAllUsers() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate, 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate, _doc_data_FIELD_NAMES_planExpiry;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate(),\n                planExpiry: (_doc_data_FIELD_NAMES_planExpiry = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.planExpiry]) === null || _doc_data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _doc_data_FIELD_NAMES_planExpiry.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting all users:', error);\n        throw error;\n    }\n}\n// Get total user count\nasync function getTotalUserCount() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.size;\n    } catch (error) {\n        console.error('Error getting total user count:', error);\n        throw error;\n    }\n}\n// Get all transactions with pagination\nasync function getTransactions() {\n    let pageSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, lastDoc = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = snapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_FIELD_NAMES_date = doc.data()[_dataService__WEBPACK_IMPORTED_MODULE_2__.FIELD_NAMES.date]) === null || _doc_data_FIELD_NAMES_date === void 0 ? void 0 : _doc_data_FIELD_NAMES_date.toDate()\n            };\n        });\n        return {\n            transactions,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        throw error;\n    }\n}\n// Get all withdrawals with pagination\nasync function getWithdrawals() {\n    let pageSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, lastDoc = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    try {\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        if (lastDoc) {\n            q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.startAfter)(lastDoc), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(pageSize));\n        }\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n        return {\n            withdrawals,\n            lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,\n            hasMore: snapshot.docs.length === pageSize\n        };\n    } catch (error) {\n        console.error('Error getting withdrawals:', error);\n        throw error;\n    }\n}\n// Get all pending withdrawals without pagination (for admin full view)\nasync function getAllPendingWithdrawals() {\n    try {\n        console.log('🔍 Loading ALL pending withdrawals...');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n        console.log(\"✅ Loaded \".concat(withdrawals.length, \" pending withdrawals\"));\n        return withdrawals;\n    } catch (error) {\n        console.error('Error getting all pending withdrawals:', error);\n        throw error;\n    }\n}\n// Get all withdrawals without pagination (for admin full view)\nasync function getAllWithdrawals() {\n    try {\n        console.log('🔍 Loading ALL withdrawals...');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const withdrawals = snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n        console.log(\"✅ Loaded \".concat(withdrawals.length, \" total withdrawals\"));\n        return withdrawals;\n    } catch (error) {\n        console.error('Error getting all withdrawals:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUser(userId, updateData) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users, userId), updateData);\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error updating user:', error);\n        throw error;\n    }\n}\n// Delete user\nasync function deleteUser(userId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.users, userId));\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error deleting user:', error);\n        throw error;\n    }\n}\n// Update withdrawal status\nasync function updateWithdrawalStatus(withdrawalId, status, adminNotes) {\n    try {\n        // Get withdrawal details first\n        const withdrawalDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals, withdrawalId));\n        if (!withdrawalDoc.exists()) {\n            throw new Error('Withdrawal not found');\n        }\n        const withdrawalData = withdrawalDoc.data();\n        const { userId, amount, status: currentStatus } = withdrawalData;\n        const updateData = {\n            status,\n            updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        if (adminNotes) {\n            updateData.adminNotes = adminNotes;\n        }\n        // Update withdrawal status\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, _dataService__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.withdrawals, withdrawalId), updateData);\n        // If withdrawal is being approved, just add a transaction record (amount already debited)\n        if (status === 'approved' && currentStatus !== 'approved') {\n            const { addTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n            // Add transaction record for withdrawal approval\n            await addTransaction(userId, {\n                type: 'withdrawal_approved',\n                amount: 0,\n                description: \"Withdrawal approved - ₹\".concat(amount, \" processed for transfer\")\n            });\n        }\n        // If withdrawal is being rejected, credit back the amount (since it was already debited)\n        if (status === 'rejected' && currentStatus !== 'rejected') {\n            const { updateWalletBalance, addTransaction } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n            // Credit back the amount to user's wallet\n            await updateWalletBalance(userId, amount);\n            // Add transaction record for withdrawal credit back\n            await addTransaction(userId, {\n                type: 'withdrawal_rejected',\n                amount: amount,\n                description: \"Withdrawal rejected - ₹\".concat(amount, \" credited back to wallet\")\n            });\n        }\n        // Clear relevant cache\n        adminCache.delete('dashboard-stats');\n    } catch (error) {\n        console.error('Error updating withdrawal status:', error);\n        throw error;\n    }\n}\n// Clear admin cache\nfunction clearAdminCache() {\n    adminCache.clear();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/adminDataService.ts\n"));

/***/ })

}]);