"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_versionService_ts-_0bc81";
exports.ids = ["_ssr_src_lib_versionService_ts-_0bc81"];
exports.modules = {

/***/ "(ssr)/./src/lib/versionService.ts":
/*!***********************************!*\
  !*** ./src/lib/versionService.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkVersionAndClearCache: () => (/* binding */ checkVersionAndClearCache),\n/* harmony export */   clearApplicationCache: () => (/* binding */ clearApplicationCache),\n/* harmony export */   clearApplicationCacheSilently: () => (/* binding */ clearApplicationCacheSilently),\n/* harmony export */   getVersionInfo: () => (/* binding */ getVersionInfo),\n/* harmony export */   manualCacheClear: () => (/* binding */ manualCacheClear),\n/* harmony export */   simpleCacheClear: () => (/* binding */ simpleCacheClear),\n/* harmony export */   updateServerVersion: () => (/* binding */ updateServerVersion)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n// Version management service to handle automatic cache clearing on deployments\n\n\n// Current app version - increment this with each deployment\nconst CURRENT_VERSION = '1.1.0';\n// Version tracking in Firestore\nconst VERSION_DOC_PATH = 'system/version';\n// Local storage keys\nconst LOCAL_VERSION_KEY = 'mytube_app_version';\nconst LAST_CACHE_CLEAR_KEY = 'mytube_last_cache_clear';\n// Check if app version has changed and clear cache if needed (SILENT)\nasync function checkVersionAndClearCache() {\n    try {\n        console.log('🔍 Silently checking app version for cache management...');\n        // Get stored local version\n        const localVersion = localStorage.getItem(LOCAL_VERSION_KEY);\n        // Get server version from Firestore\n        let serverVersion = CURRENT_VERSION;\n        try {\n            const versionDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, VERSION_DOC_PATH));\n            if (versionDoc.exists()) {\n                serverVersion = versionDoc.data()?.version || CURRENT_VERSION;\n            }\n        } catch (error) {\n            console.warn('Could not fetch server version, using current version:', error);\n        }\n        // Check if version has changed\n        const versionChanged = !localVersion || localVersion !== serverVersion;\n        if (versionChanged) {\n            console.log(`🔄 Version changed: ${localVersion || 'none'} → ${serverVersion} - Clearing cache silently...`);\n            // Clear cache silently in background\n            try {\n                await clearApplicationCacheSilently();\n                // Update local version\n                localStorage.setItem(LOCAL_VERSION_KEY, serverVersion);\n                localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString());\n                console.log('✅ Cache cleared silently due to version update');\n                // Silently reload the page after a short delay to apply changes\n                setTimeout(()=>{\n                    window.location.reload();\n                }, 1000);\n                return true;\n            } catch (error) {\n                console.error('Silent cache clear failed, continuing normally:', error);\n                // Don't disrupt user experience if silent clear fails\n                localStorage.setItem(LOCAL_VERSION_KEY, serverVersion);\n                return false;\n            }\n        }\n        console.log('✅ Version unchanged, no cache clearing needed');\n        return false;\n    } catch (error) {\n        console.error('Error checking version:', error);\n        return false;\n    }\n}\n// Silent cache clearing (no user interaction, faster, background)\nasync function clearApplicationCacheSilently() {\n    try {\n        console.log('🔇 Silently clearing application cache...');\n        // Clear localStorage (except authentication data) - preserve user session\n        const authKeys = [\n            'firebase:authUser',\n            'firebase:host'\n        ];\n        const preservedData = {};\n        // Preserve auth data\n        authKeys.forEach((key)=>{\n            const keys = Object.keys(localStorage).filter((k)=>k.includes(key));\n            keys.forEach((k)=>{\n                preservedData[k] = localStorage.getItem(k);\n            });\n        });\n        // Clear all localStorage\n        localStorage.clear();\n        // Restore auth data\n        Object.entries(preservedData).forEach(([key, value])=>{\n            if (value) {\n                localStorage.setItem(key, value);\n            }\n        });\n        // Clear sessionStorage\n        sessionStorage.clear();\n        // Clear browser cache silently (with shorter timeouts)\n        if ('caches' in window) {\n            try {\n                const cacheNames = await Promise.race([\n                    caches.keys(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache keys timeout')), 2000))\n                ]);\n                await Promise.race([\n                    Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName))),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache deletion timeout')), 2000))\n                ]);\n            } catch (error) {\n                // Silently ignore cache clearing errors\n                console.warn('Silent cache clear skipped browser caches:', error);\n            }\n        }\n        // Skip IndexedDB clearing in silent mode to avoid blocking\n        // It will be cleared on next manual clear if needed\n        console.log('✅ Silent cache clear completed');\n    } catch (error) {\n        console.warn('Silent cache clear encountered error:', error);\n    // Don't throw error in silent mode\n    }\n}\n// Clear all application cache and data (full version with user feedback)\nasync function clearApplicationCache() {\n    try {\n        console.log('🧹 Clearing application cache and data...');\n        // Clear localStorage (except authentication data)\n        const authKeys = [\n            'firebase:authUser',\n            'firebase:host'\n        ];\n        const preservedData = {};\n        // Preserve auth data\n        authKeys.forEach((key)=>{\n            const keys = Object.keys(localStorage).filter((k)=>k.includes(key));\n            keys.forEach((k)=>{\n                preservedData[k] = localStorage.getItem(k);\n            });\n        });\n        // Clear all localStorage\n        localStorage.clear();\n        // Restore auth data\n        Object.entries(preservedData).forEach(([key, value])=>{\n            if (value) {\n                localStorage.setItem(key, value);\n            }\n        });\n        // Clear sessionStorage\n        sessionStorage.clear();\n        console.log('🗑️ Storage cleared');\n        // Clear browser cache if supported\n        if ('caches' in window) {\n            try {\n                const cacheNames = await Promise.race([\n                    caches.keys(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache keys timeout')), 5000))\n                ]);\n                await Promise.race([\n                    Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName))),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache deletion timeout')), 5000))\n                ]);\n                console.log('🗑️ Browser caches cleared');\n            } catch (error) {\n                console.warn('Could not clear browser caches:', error);\n            }\n        }\n        // Clear IndexedDB with timeout protection\n        if ('indexedDB' in window) {\n            try {\n                // Use a more conservative approach for IndexedDB\n                const dbNames = [\n                    'firebaseLocalStorageDb',\n                    'firebase-heartbeat-database',\n                    'firebase-installations-database'\n                ];\n                for (const dbName of dbNames){\n                    try {\n                        await Promise.race([\n                            new Promise((resolve)=>{\n                                const deleteReq = indexedDB.deleteDatabase(dbName);\n                                deleteReq.onsuccess = ()=>resolve();\n                                deleteReq.onerror = ()=>resolve() // Don't fail if DB doesn't exist\n                                ;\n                                deleteReq.onblocked = ()=>resolve() // Continue even if blocked\n                                ;\n                                // Timeout after 3 seconds\n                                setTimeout(()=>resolve(), 3000);\n                            }),\n                            new Promise((_, reject)=>setTimeout(()=>reject(new Error('IndexedDB timeout')), 5000))\n                        ]);\n                    } catch (error) {\n                        console.warn(`Could not clear IndexedDB ${dbName}:`, error);\n                    }\n                }\n                console.log('🗑️ IndexedDB cleared');\n            } catch (error) {\n                console.warn('Could not clear IndexedDB:', error);\n            }\n        }\n        console.log('✅ Application cache cleared successfully');\n    } catch (error) {\n        console.error('Error clearing cache:', error);\n        throw error;\n    }\n}\n// Update server version (admin function)\nasync function updateServerVersion(newVersion) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, VERSION_DOC_PATH), {\n            version: newVersion,\n            updatedAt: new Date().toISOString(),\n            updatedBy: 'admin'\n        });\n        console.log(`✅ Server version updated to ${newVersion}`);\n    } catch (error) {\n        console.error('Error updating server version:', error);\n        throw error;\n    }\n}\n// Simple cache clear (fallback function)\nfunction simpleCacheClear() {\n    try {\n        // Clear storage\n        localStorage.clear();\n        sessionStorage.clear();\n        // Clear cookies\n        document.cookie.split(\";\").forEach((cookie)=>{\n            const eqPos = cookie.indexOf(\"=\");\n            const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();\n            if (name) {\n                document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\n            }\n        });\n        console.log('✅ Simple cache clear completed');\n    } catch (error) {\n        console.error('Error in simple cache clear:', error);\n    }\n}\n// Get current version info\nfunction getVersionInfo() {\n    return {\n        currentVersion: CURRENT_VERSION,\n        localVersion: localStorage.getItem(LOCAL_VERSION_KEY),\n        lastCacheClear: localStorage.getItem(LAST_CACHE_CLEAR_KEY)\n    };\n}\n// Manual cache clear function for users\nasync function manualCacheClear() {\n    try {\n        // Add timeout to the entire operation\n        await Promise.race([\n            clearApplicationCache(),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error('Cache clear operation timed out')), 15000))\n        ]);\n        localStorage.setItem(LAST_CACHE_CLEAR_KEY, new Date().toISOString());\n        // Show success message and reload\n        alert('Cache cleared successfully! The page will now reload to apply changes.');\n        window.location.reload();\n    } catch (error) {\n        console.error('Error during manual cache clear:', error);\n        // Try a simple reload as fallback\n        const fallback = confirm('Cache clearing encountered an issue. Would you like to try a simple page refresh instead?');\n        if (fallback) {\n            window.location.reload();\n        } else {\n            alert('Please try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)');\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/versionService.ts\n");

/***/ })

};
;