'use client'

import { useState } from 'react'
import { optimizedService } from '@/lib/optimizedDataService'
import { useAuthState } from '@/hooks/useAuth'

export default function TestFunctionsPage() {
  const { user } = useAuthState()
  const [testResults, setTestResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const addResult = (test: string, success: boolean, data: any, error?: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      error,
      timestamp: new Date().toISOString()
    }])
  }

  const testFunctions = async () => {
    setLoading(true)
    setTestResults([])

    // Test 1: Check if functions are available
    try {
      console.log('🔍 Testing functions availability...')
      const available = await optimizedService.areFunctionsAvailable()
      addResult('Functions Availability', available, { available })
    } catch (error) {
      addResult('Functions Availability', false, null, error)
    }

    // Test 2: Test dashboard data (if user is logged in)
    if (user) {
      try {
        console.log('🔍 Testing dashboard data...')
        const dashboardData = await optimizedService.getOptimizedDashboardData(user.uid)
        addResult('Dashboard Data', true, dashboardData)
      } catch (error) {
        addResult('Dashboard Data', false, null, error)
      }
    }

    // Test 3: Test admin withdrawals (if user is admin)
    if (user && ['<EMAIL>', '<EMAIL>'].includes(user.email || '')) {
      try {
        console.log('🔍 Testing admin withdrawals...')
        const withdrawalsData = await optimizedService.getOptimizedAdminWithdrawals(false)
        addResult('Admin Withdrawals', true, withdrawalsData)
      } catch (error) {
        addResult('Admin Withdrawals', false, null, error)
      }
    }

    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Firebase Functions Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <div className="space-y-2">
            <p><strong>User:</strong> {user ? user.email : 'Not logged in'}</p>
            <p><strong>Project ID:</strong> {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}</p>
            <p><strong>Auth Domain:</strong> {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <button
            onClick={testFunctions}
            disabled={loading}
            className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Firebase Functions'}
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className={`p-4 rounded border-l-4 ${
                  result.success ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{result.test}</h3>
                    <span className={`px-2 py-1 rounded text-sm ${
                      result.success ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'
                    }`}>
                      {result.success ? 'SUCCESS' : 'FAILED'}
                    </span>
                  </div>
                  
                  {result.success && result.data && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 mb-1">Response Data:</p>
                      <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  {!result.success && result.error && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 mb-1">Error Details:</p>
                      <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify({
                          name: result.error.name,
                          message: result.error.message,
                          code: result.error.code,
                          details: result.error.details
                        }, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  <p className="text-xs text-gray-500 mt-2">
                    {new Date(result.timestamp).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
